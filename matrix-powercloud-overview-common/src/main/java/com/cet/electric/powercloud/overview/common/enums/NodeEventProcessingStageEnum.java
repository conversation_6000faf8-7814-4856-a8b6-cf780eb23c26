package com.cet.electric.powercloud.overview.common.enums;

import lombok.Getter;

/**
 * 事件处理状态枚举
 *
 * <AUTHOR>
 * @date 2022/12/5
 */
@Getter
public enum NodeEventProcessingStageEnum {
    UNCONFIRMED(1, "存在未处理的事件"),
    CONFIRMING(2, "不存在未处理，但存在处理中的事件"),
    CONFIRMED(3, "不存在未处理和处理中的事件");

    private final int code;
    private final String message;

    NodeEventProcessingStageEnum(final int code, final String message) {
        this.code = code;
        this.message = message;
    }

    public static Integer getEventStatus(long unconfirmedCount, long confirmingCount) {
        if (unconfirmedCount > 0) {
            return NodeEventProcessingStageEnum.UNCONFIRMED.getCode();
        } else if (confirmingCount > 0) {
            return NodeEventProcessingStageEnum.CONFIRMING.getCode();
        } else {
            return NodeEventProcessingStageEnum.CONFIRMED.getCode();
        }
    }
}
