package com.cet.electric.powercloud.overview.common.enums;

import com.cet.electric.powercloud.common.enums.common.ALanguageEnum;
import com.cet.futureblue.i18n.LanguageUtil;
import lombok.Getter;

import java.util.Objects;

@Getter
public enum EnergyUsageCurveTypeEnum {
    /**
     * 尖
     */
    TOP(1, "尖"),

    /**
     * 峰
     */
    PEAK(2, "峰"),

    /**
     * 平
     */
    FLAT(3, "平"),

    /**
     * 谷
     */
    VALLY(4, "谷");

    private final int code;

    private final String name;

    EnergyUsageCurveTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static EnergyUsageCurveTypeEnum getByName(String name) {
        for (EnergyUsageCurveTypeEnum value : EnergyUsageCurveTypeEnum.values()) {
            if (Objects.equals(value.name, name)) {
                return value;
            }
        }
        throw new IllegalArgumentException(LanguageUtil.getMessage(ALanguageEnum.ERROR_ENUM_INVALID.getKey()));
    }
}
