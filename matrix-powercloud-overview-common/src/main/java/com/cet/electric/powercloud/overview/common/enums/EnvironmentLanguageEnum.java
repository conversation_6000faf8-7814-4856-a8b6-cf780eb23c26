package com.cet.electric.powercloud.overview.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: ma<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/3/1 14:52
 * @description:
 */
@AllArgsConstructor
@Getter
public enum EnvironmentLanguageEnum {
    /**
     * 环境监测国际化
     */
    ENVIRONMENT_INFO_ENVIRONMENT_SERVICE_QUERYDEVICERELATION_UP(8001, "environment.info.environment.service.queryDeviceRelation.up"),
    ENVIRONMENT_INFO_ENVIRONMENT_SERVICE_QUERYDEVICERELATION_DOWN(8002, "environment.info.environment.service.queryDeviceRelation.down"),
    ENVIRONMENT_ERROR_VIDEO_SERVICE_QUERYROOMCAMERAS(8003, "environment.error.video.service.queryRoomCameras"),
    ENVIRONMENT_INFO_VIDEO_SERVICE_DELETECAMERAS(8004, "environment.info.video.service.deleteCameras"),
    ENVIRONMENT_ERROR_VIDEO_SERVICE_EDITNAME(8005, "environment.error.video.service.editName"),
    ENVIRONMENT_INFO_VIDEO_SERVICE_EDITNAME(8006, "environment.info.video.service.editName"),
    ENVIRONMENT_INFO_VIDEO_SERVICE_ADD(8007, "environment.info.video.service.add"),
    ENVIRONMENT_ERROR_VIDEO_SERVICE_DUPNAME(8008, "environment.error.video.service.dupName");

    private final Integer code;

    private final String msg;

    @Override
    public String toString() {
        return "EnvironmentLanguageEnum{" + "code=" + code + ", msg='" + msg + '\'' + '}';
    }
}
