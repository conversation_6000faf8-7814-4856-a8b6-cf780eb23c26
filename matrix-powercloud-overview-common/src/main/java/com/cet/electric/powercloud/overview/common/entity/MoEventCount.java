package com.cet.electric.powercloud.overview.common.entity;
import com.cet.electric.modelservice.sdk.annotations.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 事件统计
 * @date 2022/10/25 19:11
 */
@Data
@TableName("mo_eventcount")
public class MoEventCount {
    private Long id;
    @JsonProperty("aggregationcycle")
    private Integer aggregationCycle;
    @JsonProperty("count")
    private Long count;
    @JsonProperty("eventlevel")
    private Integer eventLevel;
    @JsonProperty("eventtype")
    private Integer eventType;
    @JsonProperty("logtime")
    private Long logTime;
    @JsonProperty("monitoredid")
    private Long monitoredId;
    @JsonProperty("monitoredlabel")
    private String monitoredLabel;
    @JsonProperty("eventlabel")
    private String eventLabel;
}
