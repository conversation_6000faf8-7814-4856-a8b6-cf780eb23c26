package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 全局监测请求参数
 * @date 2022/11/7 16:44
 */
@Data
public class OverviewParam {
    @ApiModelProperty(value = "查询时间段开始时间(时间戳),左闭", example = "1664553600000")
    private Long startTime;
    @ApiModelProperty(value = "查询时间段结束时间(时间戳),右开", example = "1667232000000")
    private Long endTime;
    @ApiModelProperty(value = "时段", example = "14")
    private Integer aggregationCycle;
    @ApiModelProperty(value = "项目id", example = "1")
    private Long projectId;
    @ApiModelProperty(value = "楼栋id", example = "1")
    private Long buildingId;
    @ApiModelProperty(value = "配电房id", example = "1")
    private Long roomId;
}
