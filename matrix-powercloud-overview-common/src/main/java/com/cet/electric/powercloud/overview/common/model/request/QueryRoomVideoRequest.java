package com.cet.electric.powercloud.overview.common.model.request;

import com.cet.electric.powercloud.common.model.PageParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: 分页配电房摄像头
 * @date 2022/10/9 11:09
 */
@Data
@ApiModel
public class QueryRoomVideoRequest {
    @NotNull
    @ApiModelProperty(value = "配电房id", required = true, example = "1")
    private Long roomId;
    @ApiModelProperty(value = "清晰度", example = "1")
    private Integer clarity;
    private PageParams pageParams;
}
