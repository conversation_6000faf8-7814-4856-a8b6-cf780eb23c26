package com.cet.electric.powercloud.overview.common.entity;

import com.cet.electric.modelservice.sdk.annotations.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Copyright © 1993-2025 深圳市中电电力技术股份有限公司.All rights reserved.
 *
 * @author: z<PERSON>jie230121 张杰
 * @date: 2025-02-24  18:45
 * @description:
 */
@Data
@TableName("wisdomrobot")
public class WisdomRobot {
    private Long id;
    private String name;
    private String code;
    private String ip;
    private Integer port;
    @JsonProperty("tracktype")
    private Integer trackType;
    @JsonProperty("tracktypetext")
    private String trackTypeText;
    @JsonProperty("robottype")
    private Integer robotType;
    @JsonProperty("robottypetext")
    private String robotTypeText;
    @JsonProperty("cameraid")
    private Long cameraId;

    @JsonProperty("objectlabel")
    private String objectLabel;
    @JsonProperty("objectid")
    private Long objectId;
}
