package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 用电概览返回
 * @date 2022/11/8 9:41
 */
@Data
public class ElectricAnalysisResponse {
    @ApiModelProperty("今日用电量")
    private Double todayEnergyUsage;
    @ApiModelProperty("昨日用电量")
    private Double yesterdayEnergyUsage;
    @ApiModelProperty("本月用电量")
    private Double monthEnergyUsage;
    @ApiModelProperty("上月用电量")
    private Double lastMonthEnergyUsage;
    @ApiModelProperty("日环比")
    private Double dayPeriodOverPeriodChange;
    @ApiModelProperty("月环比")
    private Double monthPeriodOverPeriodChange;
}
