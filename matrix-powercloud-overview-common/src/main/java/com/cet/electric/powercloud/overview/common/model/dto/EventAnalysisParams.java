package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 事件分析请求参数
 * @date 2022/10/26 15:37
 */
@Data
public class EventAnalysisParams {
    @ApiModelProperty(value = "查询时间段开始时间(时间戳),左闭", example = "1664553600000")
    private Long startTime;
    @ApiModelProperty(value = "查询时间段结束时间(时间戳),右开", example = "1667232000000")
    private Long endTime;
    @ApiModelProperty(value = "时段", example = "12")
    private Integer aggregationCycle;
    @ApiModelProperty(value = "模型节点label")
    private String modelLabel;
    @ApiModelProperty(value = "模型节点id列表")
    private List<Long> modelIds;
    @ApiModelProperty(value = "处理状态", example = "1")
    private List<Integer> confirmEventStatusList;
    @ApiModelProperty(value = "事件等级", example = "1")
    private List<Integer> eventLevelList;
    @ApiModelProperty(value = "事件类型", example = "1")
    private List<Integer> eventTypeList;
    @ApiModelProperty(value = "条数限制", example = "50")
    private Integer limit;
}
