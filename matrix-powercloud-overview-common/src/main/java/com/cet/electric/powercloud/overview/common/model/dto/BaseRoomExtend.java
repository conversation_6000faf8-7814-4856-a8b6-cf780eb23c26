package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新能源站扩展属性
 *
 * <AUTHOR>
 * @date 2023/11/15
 */
@Data
public class BaseRoomExtend {
    @ApiModelProperty("总装机容量(mW)")
    private Double totalInstalledCapacity;
    @ApiModelProperty("总额定功率(kW)")
    private Double totalRatedPower;
    @ApiModelProperty("并网电压(kV)")
    private Double girdVoltage;
}
