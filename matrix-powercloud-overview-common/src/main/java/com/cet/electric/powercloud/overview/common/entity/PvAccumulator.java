package com.cet.electric.powercloud.overview.common.entity;
import com.cet.electric.modelservice.sdk.annotations.TableName;
import com.cet.electric.powercloud.common.model.device.BaseDeviceModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description: 蓄电池
 * @date 2023/9/7 10:42
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("pv_accumulator")
public class PvAccumulator extends BaseDeviceModel {
    @JsonProperty("capacity")
    private Double capacity;
    @JsonProperty("voltage")
    private Double voltage;
    @JsonProperty("batteryenergy")
    private Double batteryEnergy;
    @JsonProperty("maxcurrent")
    private Double maxCurrent;
    @JsonProperty("cyclelife")
    private Double cycleLife;
    @JsonProperty("reversal_switch")
    private Integer reversalSwitch;
    /**
     * 用掉的循环次数
     */
    @JsonProperty("usedcyclelife")
    private Double usedCycleLife;
}
