package com.cet.electric.powercloud.overview.common.model.dto;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cet.electric.powercloud.common.enums.DefaultCodeAndMsg;
import com.cet.electric.powercloud.common.exception.BusinessException;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * @Author: joy
 * @Date: 2021/4/7 12:14
 * @Description: 参数断言, 主要是进行一些函数参数的空或者非空、大小等条件判断
 */
public final class ParamsAssert {
    private static final Map<String, Pattern> PATTERNS = new ConcurrentHashMap<>();

    private ParamsAssert() {
    }

    /**
     * 必须为null
     */
    public static void isNull(Object value) {
        if (value == null) {
            return;
        }
        throwIllegalArgumentException();
    }

    public static void isNull(Object value, String message) {
        if (value == null) {
            return;
        }
        throwIllegalArgumentException(message);
    }

    public static void isNull(Object value, Enum errorCodeAndMsg) {
        if (value == null) {
            return;
        }
        throwIllegalArgumentException(errorCodeAndMsg);
    }


    public static void throwIllegalArgumentException() {
        throw new BusinessException(DefaultCodeAndMsg.ILLEGAL_ARGUMENT);
    }


    /**
     * 不能为null
     */
    public static void notNull(Object value, Enum errorCodeAndMsg) {
        if (value != null) {
            return;
        }
        throwIllegalArgumentException(errorCodeAndMsg);
    }

    public static void notNull(Object value) {
        if (value != null) {
            return;
        }
        throwIllegalArgumentException();
    }

    public static void notNull(Object value, String message) {
        if (value != null) {
            return;
        }
        throwIllegalArgumentException(message);
    }

    /**
     * 必须为空字符串，或为null
     */
    public static void isEmpty(String value) {
        if (CharSequenceUtil.isEmpty(value)) {
            return;
        }
        throwIllegalArgumentException();
    }

    public static void isEmpty(String value, Enum errorCodeAndMsg) {
        if (CharSequenceUtil.isEmpty(value)) {
            return;
        }
        throwIllegalArgumentException(errorCodeAndMsg);
    }

    /**
     * 不能为空字符串，且不能为null
     */
    public static void notEmpty(String value) {
        if (CharSequenceUtil.isNotEmpty(value)) {
            return;
        }
        throwIllegalArgumentException();
    }

    public static void notEmpty(String value, Enum errorCodeAndMsg) {
        if (CharSequenceUtil.isNotEmpty(value)) {
            return;
        }
        throwIllegalArgumentException(errorCodeAndMsg);
    }

    public static void notEmpty(String value, String msg) {
        if (CharSequenceUtil.isNotEmpty(value)) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    /**
     * 空集合，或为null
     */
    public static void isEmpty(Collection<?> value) {
        if (CollUtil.isEmpty(value)) {
            return;
        }
        throwIllegalArgumentException();
    }

    public static void isEmpty(Collection<?> value, String msg) {
        if (CollUtil.isEmpty(value)) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static void isEmpty(Collection<?> value, Enum codeAndMsg) {
        if (CollUtil.isEmpty(value)) {
            return;
        }
        throwIllegalArgumentException(codeAndMsg);
    }

    /**
     * 不能为空集合，且不能为null
     */
    public static void notEmpty(Collection<?> value) {
        if (CollUtil.isNotEmpty(value)) {
            return;
        }
        throwIllegalArgumentException();
    }

    public static void notEmpty(Collection<?> value, String msg) {
        if (CollUtil.isNotEmpty(value)) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static void notEmpty(Collection<?> value, Enum errorCodeAndMsg) {
        if (CollUtil.isNotEmpty(value)) {
            return;
        }
        throwIllegalArgumentException(errorCodeAndMsg);
    }


    /**
     * 不能为空白字符串，且不能为null
     */
    public static void notBlank(String value) {
        if (CharSequenceUtil.isNotBlank(value)) {
            return;
        }
        throwIllegalArgumentException();
    }

    public static void notBlank(String value, String msg) {
        if (CharSequenceUtil.isNotBlank(value)) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static void notBlank(String value, Enum errorCodeAndMsg) {
        if (CharSequenceUtil.isNotBlank(value)) {
            return;
        }

        throwIllegalArgumentException(errorCodeAndMsg);
    }

    /**
     * 必须相等，或为null
     */
    public static void eq(Object value, Object that) {
        if (value == null || value.equals(that)) {
            return;
        }
        throwIllegalArgumentException();
    }

    public static void eq(Object value, Object that, String msg) {
        if (value == null || value.equals(that)) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static void eq(Object value, Object that, Enum errorCodeAndMsg) {
        if (value == null || value.equals(that)) {
            return;
        }
        throwIllegalArgumentException(errorCodeAndMsg);
    }

    /**
     * 必须不相等，或为null
     */
    public static void ne(Object value, Object that) {
        if (value == null || !value.equals(that)) {
            return;
        }

        throwIllegalArgumentException();
    }

    public static void ne(Object value, Object that, String msg) {
        if (value == null || !value.equals(that)) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static void ne(Object value, Object that, Enum errorCodeAndMsg) {
        if (value == null || !value.equals(that)) {
            return;
        }
        throwIllegalArgumentException(errorCodeAndMsg);
    }

    /**
     * 必须小于，或为null
     */
    public static <T extends Comparable<T>> void lt(T value, T that) {
        if (value == null || value.compareTo(that) < 0) {
            return;
        }

        throwIllegalArgumentException();
    }

    public static <T extends Comparable<T>> void lt(T value, T that, String msg) {
        if (value == null || value.compareTo(that) < 0) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static <T extends Comparable<T>> void lt(T value, T that, Enum errorCodeAndMsg) {
        if (value == null || value.compareTo(that) < 0) {
            return;
        }

        throwIllegalArgumentException(errorCodeAndMsg);
    }


    /**
     * 必须小于或等于，或为null
     */
    public static <T extends Comparable<T>> void lte(T value, T that) {
        if (value == null || value.compareTo(that) <= 0) {
            return;
        }
        throwIllegalArgumentException();
    }

    public static <T extends Comparable<T>> void lte(T value, T that, String msg) {
        if (value == null || value.compareTo(that) <= 0) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static <T extends Comparable<T>> void lte(T value, T that, Enum errorCodeAndMsg) {
        if (value == null || value.compareTo(that) <= 0) {
            return;
        }

        throwIllegalArgumentException(errorCodeAndMsg);
    }

    /**
     * 必须大于，或为null
     */
    public static <T extends Comparable<T>> void gt(T value, T that) {
        if (value == null || value.compareTo(that) > 0) {
            return;
        }

        throwIllegalArgumentException();
    }

    public static <T extends Comparable<T>> void gt(T value, T that, String msg) {
        if (value == null || value.compareTo(that) > 0) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static <T extends Comparable<T>> void gt(T value, T that, Enum errorCodeAndMsg) {
        if (value == null || value.compareTo(that) > 0) {
            return;
        }

        throwIllegalArgumentException(errorCodeAndMsg);
    }

    /**
     * 必须大于或等于，或为null
     */
    public static <T extends Comparable<T>> void gte(T value, T that) {
        if (value == null || value.compareTo(that) >= 0) {
            return;
        }

        throwIllegalArgumentException();
    }

    public static <T extends Comparable<T>> void gte(T value, T that, String msg) {
        if (value == null || value.compareTo(that) >= 0) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static <T extends Comparable<T>> void gte(T value, T that, Enum errorCodeAndMsg) {
        if (value == null || value.compareTo(that) >= 0) {
            return;
        }

        throwIllegalArgumentException(errorCodeAndMsg);
    }

    /**
     * 必须在最大最小值之间（含），或为null
     */
    public static <T extends Comparable<T>> void between(T value, T min, T max) {
        if (value == null || (value.compareTo(min) >= 0 && value.compareTo(max) <= 0)) {
            return;
        }

        throwIllegalArgumentException();
    }

    public static <T extends Comparable<T>> void between(T value, T min, T max, String msg) {
        if (value == null || (value.compareTo(min) >= 0 && value.compareTo(max) <= 0)) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static <T extends Comparable<T>> void between(T value, T min, T max, Enum errorCodeAndMsg) {
        if (value == null || (value.compareTo(min) >= 0 && value.compareTo(max) <= 0)) {
            return;
        }

        throwIllegalArgumentException(errorCodeAndMsg);
    }

    /**
     * 字符串长度必须小于最大值（含），或为null
     */
    public static void maxlength(String value, int max) {
        if (value == null || value.length() <= max) {
            return;
        }

        throwIllegalArgumentException();
    }

    public static void maxlength(String value, int max, String msg) {
        if (value == null || value.length() <= max) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static void maxlength(String value, int max, Enum errorCodeAndMsg) {
        if (value == null || value.length() <= max) {
            return;
        }

        throwIllegalArgumentException(errorCodeAndMsg);
    }

    /**
     * 字符串长度必须大于最小值（含），或为null
     */
    public static void minlength(String value, int min) {
        if (value == null || value.length() >= min) {
            return;
        }

        throwIllegalArgumentException();
    }

    public static void minlength(String value, int min, String msg) {
        if (value == null || value.length() >= min) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static void minlength(String value, int min, Enum errorCodeAndMsg) {
        if (value == null || value.length() >= min) {
            return;
        }
        throwIllegalArgumentException(errorCodeAndMsg);
    }

    /**
     * 字符串长度必须在最大值最小值之间（含），或为null
     */
    public static void length(String value, int min, int max) {
        if (value == null || (min <= value.length() && value.length() <= max)) {
            return;
        }
        throwIllegalArgumentException();
    }

    public static void length(String value, int min, int max, String msg) {
        if (value == null || (min <= value.length() && value.length() <= max)) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static void length(String value, int min, int max, Enum errorCodeAndMsg) {
        if (value == null || (min <= value.length() && value.length() <= max)) {
            return;
        }

        throwIllegalArgumentException(errorCodeAndMsg);
    }

    /**
     * 值必须在给定集合中，或为null
     */
    public static void in(Object value, Collection<?> collection) {
        if (value == null || collection.contains(value)) {
            return;
        }

        throwIllegalArgumentException();
    }

    public static void in(Object value, Collection<?> collection, String msg) {
        if (value == null || collection.contains(value)) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static void in(Object value, Collection<?> collection, Enum errorCodeAndMsg) {
        if (value == null || collection.contains(value)) {
            return;
        }

        throwIllegalArgumentException(errorCodeAndMsg);
    }

    /**
     * 字符串必须匹配正则表达式，或为null
     */
    public static void pattern(String value, Pattern regex) {
        if (value == null || regex.matcher(value).matches()) {
            return;
        }

        throwIllegalArgumentException();
    }

    public static void pattern(String value, Pattern regex, String msg) {
        if (value == null || regex.matcher(value).matches()) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static void pattern(String value, String regex) {
        Pattern compiled = PATTERNS.computeIfAbsent(regex, Pattern::compile);
        pattern(value, compiled);
    }

    public static void pattern(String value, Pattern regex, Enum errorCodeAndMsg) {
        if (value == null || regex.matcher(value).matches()) {
            return;
        }

        throwIllegalArgumentException(errorCodeAndMsg);
    }

    /**
     * 字符串必须匹配正则表达式，或为null
     */
    public static void pattern(String value, String regex, String msg) {
        Pattern compiled = PATTERNS.computeIfAbsent(regex, Pattern::compile);
        pattern(value, compiled, msg);
    }

    public static void pattern(String value, String regex, Enum errorCodeAndMsg) {
        Pattern compiled = PATTERNS.computeIfAbsent(regex, Pattern::compile);
        pattern(value, compiled, errorCodeAndMsg);
    }

    /**
     * 主键必须大于0，且不为null
     */
    public static void isPrimaryKey(Number key) {
        if (key != null && key.longValue() > 0) {
            return;
        }
        throwIllegalArgumentException();
    }

    /**
     * 主键必须大于0，且不为null
     */
    public static void isPrimaryKey(Number key, String msg) {
        if (key != null && key.longValue() > 0) {
            return;
        }
        throwIllegalArgumentException(msg);
    }

    public static void isPrimaryKey(Number key, Enum errorCodeAndMsg) {
        if (key != null && key.longValue() > 0) {
            return;
        }
        throwIllegalArgumentException(errorCodeAndMsg);
    }



    /**
     * 抛出参数异常
     *
     * @param msg 需要传递的错误参数
     */
    public static void throwIllegalArgumentException(String msg) {
        if (CharSequenceUtil.isNotBlank(msg)) {
            throw new BusinessException(DefaultCodeAndMsg.ILLEGAL_ARGUMENT.getCode(), msg);
        }
        throwIllegalArgumentException();
    }


    public static void throwIllegalArgumentException(Enum errorCodeAndMsg) {
        if (errorCodeAndMsg == null) {
            throwIllegalArgumentException();
        }
        throw new BusinessException(errorCodeAndMsg);
    }
}
