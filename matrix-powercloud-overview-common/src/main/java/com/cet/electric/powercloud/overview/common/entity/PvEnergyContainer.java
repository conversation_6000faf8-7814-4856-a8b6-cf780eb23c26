package com.cet.electric.powercloud.overview.common.entity;


import com.cet.electric.modelservice.sdk.annotations.TableName;
import com.cet.electric.powercloud.common.model.device.BaseDeviceModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 储能集装箱
 * @date 2023/9/7 10:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("pv_energycontainer")
public class PvEnergyContainer extends BaseDeviceModel {
    @JsonProperty("capacity")
    private Double capacity;
    @JsonProperty("outputpower")
    private Double outputPower;
    @JsonProperty("cyclelife")
    private Double cycleLife;
    @JsonProperty("efficiency")
    private Double efficiency;
    @JsonProperty("responsetime")
    private Double responseTime;
    @JsonProperty("temperaturerange")
    private Double temperatureRange;
    @JsonProperty("pv_accumulator_model")
    private List<PvAccumulator> pvAccumulatorList;
    @JsonProperty("pcs_model")
    private List<PCS> pcsList;
}
