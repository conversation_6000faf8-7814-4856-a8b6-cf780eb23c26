package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 平台概览返回
 * @date 2022/11/7 14:30
 */
@Data
public class PlatformOverviewLowCodeResponse {
    @ApiModelProperty("项目总数")
    private Integer projectCount;
    @ApiModelProperty("站点总数")
    private Integer roomCount;
    @ApiModelProperty("安全运行天数")
    private Long safeRunDays;
    @ApiModelProperty("表计数量")
    private Integer meterCount;
    @ApiModelProperty("在线设备总数量")
    private Integer deviceCount;
    @ApiModelProperty("报警设备总数量")
    private Integer alarmDeviceCount;
    @ApiModelProperty("设备在线率")
    private Double deviceOnlineRate;
    @ApiModelProperty("预警设备占比")
    private Double alarmDevicePercent;
    @ApiModelProperty("本月告警总数")
    private Integer alarmCount;
}
