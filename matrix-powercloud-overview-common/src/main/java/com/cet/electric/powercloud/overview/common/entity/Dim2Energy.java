package com.cet.electric.powercloud.overview.common.entity;

import com.cet.electric.baseconfig.common.base.BaseEntity;
import com.cet.electric.modelservice.sdk.annotations.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.text.Collator;
import java.util.Comparator;
import java.util.Locale;

/**
 * <AUTHOR> Cui
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "维度映射关系")
@TableName("dim2energy")
public class Dim2Energy extends BaseEntity implements Comparable<Dim2Energy>{

    @JsonProperty("tagid")
    @ApiModelProperty("维度标签id")
    private Long tagid;

    @ApiModelProperty("能源类型")
    @JsonProperty("energytype")
    private Integer energytype;

    @JsonProperty("objectid")
    @ApiModelProperty("对象模型id")
    private Long objectId;

    @JsonProperty("enabletime")
    @ApiModelProperty("生效时间")
    private Long enableTime;

    @JsonProperty("createtime")
    @ApiModelProperty("创建时间")
    private Long createtime;

    @JsonProperty("objectlabel")
    @ApiModelProperty("对象模型modelLabel")
    private String objectLabel;

    @JsonProperty("levelid")
    @ApiModelProperty("维度层级id")
    private Long levelid;

    @JsonProperty("disabletime")
    @ApiModelProperty("失效时间")
    private Long disabletime;

    @ApiModelProperty("设备id")
    private Long modelid;

    @ApiModelProperty("设备id")
    private String label;
    @Override
    public int compareTo(Dim2Energy o) {
        Comparator<Object> cmp = Collator.getInstance(Locale.TRADITIONAL_CHINESE);
        return cmp.compare(this.objectId, o.getObjectId());
    }
}
