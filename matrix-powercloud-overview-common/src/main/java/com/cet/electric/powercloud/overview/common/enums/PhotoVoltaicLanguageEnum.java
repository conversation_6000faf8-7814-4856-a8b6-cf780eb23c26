package com.cet.electric.powercloud.overview.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: ma<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/2/26 16:56
 * @description:
 */
@AllArgsConstructor
@Getter
public enum PhotoVoltaicLanguageEnum {

    NORMAL(120001, "info.photovoltaic.energystorage.status.normal"),
    ABNORMAL(120002, "info.photovoltaic.energystorage.status.abnormal"),
    ACCIDENT(120003, "info.photovoltaic.energystorage.status.accident"),
    ERROR_CS_PARA_VALUE(120004, "photovoltaic.error.cs.paraValue"),
    ERROR_CS_PASSWORD(120005, "photovoltaic.error.cs.password"),
    INFO_BM_UNKNOWN0(120006, "photovoltaic.info.bm.unknow"),
    UN_KNOW_STATUS(120045,"未关联"),
    INFO_BM_UNKNOWN1(120006, "photovoltaic.info.bm.breakdown"),
    INFO_BM_UNKNOWN2(120006, "photovoltaic.info.bm.giveanalarm"),
    INFO_BM_UNKNOWN3(120006, "photovoltaic.info.bm.befilledwith"),
    INFO_BM_UNKNOWN4(120006, "photovoltaic.info.bm.emptying"),
    INFO_BM_UNKNOWN6(120006, "photovoltaic.info.bm.offline"),
    INFO_BM_UNKNOWN7(120006, "photovoltaic.info.bm.balance"),
    INFO_BM_UNKNOWN8(120006, "photovoltaic.info.bm.recharge"),
    INFO_BM_UNKNOWN9(120006, "photovoltaic.info.bm.electricdischarge"),
    INFO_BM_UNKNOWN10(120006, "photovoltaic.info.bm.shutdown"),
    INFO_CSM_PEAK(120007, "photovoltaic.info.csm.peak"),
    INFO_CSM_PLAIN(120008, "photovoltaic.info.csm.plain"),
    INFO_CSM_SHOULDER(120009, "photovoltaic.info.csm.shoulder"),
    INFO_CSM_VALLEY(120010, "photovoltaic.info.csm.valley"),
    ERROR_ESS_HIERARCHY(120011, "photovoltaic.error.ess.hierarchy"),
    INFO_IA_P_STRING(120012, "photovoltaic.info.ia.pString"),
    INFO_IA_STRING(120013, "photovoltaic.info.ia.string"),
    INFO_IA_STRING_FACTOR(120014, "photovoltaic.info.ia.stringFactor"),
    INFO_IA_DESC(120015, "photovoltaic.info.ia.desc"),
    INFO_IA_DESC1(120016, "photovoltaic.info.ia.desc1"),
    INFO_IA_DESC2(120017, "photovoltaic.info.ia.desc2"),
    INFO_IA_ALL(120018, "photovoltaic.info.ia.all"),
    INFO_IA_QUALIFIED(120019, "photovoltaic.info.ia.qualified"),
    INFO_IA_UNQUALIFIED(120020, "photovoltaic.info.ia.unqualified"),
    INFO_IA_THEY(120021, "photovoltaic.info.ia.they"),
    ERROR_PIE_EMPTY_IID(120022, "photovoltaic.error.pie.emptyIId"),
    ERROR_PIE_EMPTY_ID(120023, "photovoltaic.error.pie.emptyId"),
    ERROR_PIE_EMPTY_INFO(120024, "photovoltaic.error.pie.emptyInfo"),
    ERROR_PIE_EMPTY_INVERTER(120025, "photovoltaic.error.pie.emptyInverter"),
    INFO_PG_NAME(120026, "photovoltaic.info.pg.name"),
    INFO_PG_REPORT_NAME(120027, "photovoltaic.info.pg.reportName"),
    INFO_PG_SUM_VALUE(120028, "photovoltaic.info.pg.sumValue"),
    ERROR_TASK_DISABLE(120029, "photovoltaic.error.task.disable"),
    ERROR_WF_TYPE(120030, "photovoltaic.error.wf.type"),
    INFO_WAT_DESC1(120031, "photovoltaic.info.wat.desc1"),
    INFO_WAT_DESC2(120032, "photovoltaic.info.wat.desc2"),
    INFO_TASK_SHIELDING(120033, "photovoltaic.info.task.shielding"),
    INFO_NODENAME(120034, "photovoltaic.info.export.nodeName"),
    INFO_FILENAME(120035, "photovoltaic.info.export.fileName"),
    INFO_CHANNALNAME(120036, "photovoltaic.info.export.channelName"),
    INFO_STATION_NAME(120037, "photovoltaic.info.export.stationName"),
    INFO_STATION_INDICATOR_NAME(120038, "photovoltaic.info.export.stationIndicator.fileName"),
    INFO_FILENAME2(120039, "photovoltaic.info.export.fileName2"),
    ERROR_FAN_COMPONENTDUP(120040, "photovoltaic.error.fan.componentDup"),
    POWER_OUTPUT(120041,"photovoltaic.info.datetype.poweroutput"),
    RADIATION(120042,"photovoltaic.info.datetype.radiation"),
    EQUIVALENTOUTPUTHOUR(120043,"photovoltaic.info.datetype.equivalentoutputhour"),
    OUTPUT_PLAN(120044,"photovoltaic.info.datetype.outputplan");



    private final Integer code;

    private final String msg;

    @Override
    public String toString() {
        return "PhotoVoltaicLanguageEnum{" + "code=" + code + ", msg='" + msg + '\'' + '}';
    }
}
