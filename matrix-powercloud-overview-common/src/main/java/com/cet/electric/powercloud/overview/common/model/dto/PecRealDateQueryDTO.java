package com.cet.electric.powercloud.overview.common.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 测点设备实时数据查询体
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class PecRealDateQueryDTO {
    @JsonProperty(value = "dataId")
    private Long dataId;
    @JsonProperty(value = "logicalId")
    private Integer logicalId;
    @JsonProperty(value = "deviceId")
    private Long deviceId;
}
