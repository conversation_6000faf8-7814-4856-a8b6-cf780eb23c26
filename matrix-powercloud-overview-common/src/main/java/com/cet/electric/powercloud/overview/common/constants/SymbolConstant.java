package com.cet.electric.powercloud.overview.common.constants;

/**
 * @Author: joy
 * @Date: 2021/4/7 14:21
 * @Description: 符号常量
 */
public final class SymbolConstant {
    public static final String SPOT = ".";
    public static final String COMMA = ",";
    public static final String COMMA_CHINESE = "，";
    public static final String COLON = ":";
    public static final String SEMICOLON = ";";
    public static final String EXCLAMATION_MARK = "!";
    public static final String QUESTION_MARK = "?";
    public static final String HYPHEN = "-";
    public static final String ASTERISK = "*";
    public static final String APOSTROPHE = "`";
    public static final String DASH = "-";
    public static final String DASH_DOUBLE = "--";
    public static final String UNDER_SCORE = "_";
    public static final String SINGLE_QUOTATION_MARK = "'";
    public static final String DOUBLE_QUOTATION_MARK = "\"";
    public static final String LEFT_ROUND_BRACKETS = "(";
    public static final String RIGHT_ROUND_BRACKETS = ")";
    public static final String LEFT_SQUARE_BRACKETS = "[";
    public static final String RIGHT_SQUARE_BRACKETS = "]";
    public static final String LEFT_ANGLE_BRACKETS = "<";
    public static final String RIGHT_ANGLE_BRACKETS = ">";
    public static final String LEFT_CURLY_BRACKETS = "{";
    public static final String RIGHT_CURLY_BRACKETS = "}";
    public static final String DOLLAR = "$";
    public static final String NUMBER_SIGN = "#";
    public static final String PERCENT = "%";
    public static final String LEFT_DIVIDE = "/";
    public static final String RIGHT_DIVIDE = "\\";
    public static final String LEFT_DOUBLE_DIVIDE = "//";
    public static final String RIGHT_DOUBLE_DIVIDE = "\\\\";
    public static final String BLANK_SPACE = " ";
    public static final String PAUSE = "、";
    public static final String WAVY_DASH = "~";
    public static final String FILE_SEPARATOR = System.getProperty("file.separator");

    private SymbolConstant() {
    }
}
