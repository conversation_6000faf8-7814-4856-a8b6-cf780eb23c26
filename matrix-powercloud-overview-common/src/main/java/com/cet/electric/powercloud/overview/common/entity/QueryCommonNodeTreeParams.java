package com.cet.electric.powercloud.overview.common.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 查询节点树参数
 * @date 2022/10/31 14:09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryCommonNodeTreeParams {
    private Long theUserId;
    private Boolean byAuth;
    private Boolean secondaryAuth;
    private String rootLabel;
    private Long rootId;
    private List<Long> rootIds;
    private List<String> subLabelList;
    private Integer energyType;
    private List<Integer> roomTypes;
}
