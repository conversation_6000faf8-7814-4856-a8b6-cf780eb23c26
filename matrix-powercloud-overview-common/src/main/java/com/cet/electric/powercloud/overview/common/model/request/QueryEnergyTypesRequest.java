package com.cet.electric.powercloud.overview.common.model.request;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 能耗分析-节点对比请求参数
 * @date 2022/10/21 10:13
 */
@Data
public class QueryEnergyTypesRequest {

    @ApiModelProperty(value = "查询时间段开始时间(时间戳),左闭", example = "1664553600000")
    private Long startTime;

    @ApiModelProperty(value = "查询时间段结束时间(时间戳),右开", example = "1667232000000")
    private Long endTime;

    @ApiModelProperty(value = "节点id", example = "1")
    private Long projectId;


}
