package com.cet.electric.powercloud.overview.common.entity;
import com.cet.electric.modelservice.sdk.annotations.TableName;
import com.cet.electric.powercloud.common.model.device.Project;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 区域模型
 * @date 2022/9/23 9:20
 */
@Data
@TableName("district")
public class District {
    private Long id;
    @JsonProperty("name")
    private String name;
    @JsonProperty("code")
    private Long code;
    @JsonProperty("director")
    private String director;
    @JsonProperty("directoremail")
    private String directorEmail;
    @JsonProperty("directornumber")
    private String directorNumber;
    @JsonProperty("project_model")
    private List<Project> projectList;
}
