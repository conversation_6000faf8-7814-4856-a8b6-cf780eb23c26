package com.cet.electric.powercloud.overview.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/5/24 15:29
 */
public class DoubleUtil {

    private DoubleUtil() {
    }

    // 默认除法运算精度
    private static final Integer DEF_DIV_SCALE = 100;
    // 允许的误差范围
    private static final Double EPSILON = 0.000000000001D;

    /**
     * 提供精确的加法运算。
     *
     * @param value1 被加数
     * @param value2 加数
     * @return 两个参数的和
     */
    public static Double add(Double value1, Double value2) {
        if (value1 == null && value2 == null) {
            return null;
        }
        if (value1 == null) {
            value1 = 0d;
        }
        if (value2 == null) {
            value2 = 0d;
        }
        BigDecimal b1 = new BigDecimal(Double.toString(value1));
        BigDecimal b2 = new BigDecimal(Double.toString(value2));
        return b1.add(b2).doubleValue();
    }

    /**
     * 提供精确的减法运算。
     *
     * @param value1 被减数
     * @param value2 减数
     * @return 两个参数的差
     */
    public static Double sub(Double value1, Double value2) {
        if (value1 == null && value2 == null) {
            return null;
        }
        if (value1 == null) {
            value1 = 0d;
        }
        if (value2 == null) {
            value2 = 0d;
        }
        BigDecimal b1 = new BigDecimal(Double.toString(value1));
        BigDecimal b2 = new BigDecimal(Double.toString(value2));
        return b1.subtract(b2).doubleValue();
    }

    /**
     * 提供精确的乘法运算。
     *
     * @param value1 被乘数
     * @param value2 乘数
     * @return 两个参数的积
     */
    public static Double mul(Double value1, Double value2) {
        if (value1 == null || value2 == null) {
            return null;
        }
        BigDecimal b1 = new BigDecimal(Double.toString(value1));
        BigDecimal b2 = new BigDecimal(Double.toString(value2));
        return b1.multiply(b2).doubleValue();
    }

    /**
     * 将一个Double类型的数据乘以100
     *
     * @return 乘以100的结果
     */
    public static Double multiplyByHundred(Double value) {
        if (value == null) {
            return null;
        }
        int hundred = 100;
        BigDecimal b1 = new BigDecimal(Double.toString(value));
        BigDecimal b2 = new BigDecimal(Integer.toString(hundred));
        return b1.multiply(b2).doubleValue();
    }

    /**
     * 提供（相对）精确的除法运算，当发生除不尽的情况时， 精确到小数点以后10位，以后的数字四舍五入。
     *
     * @param dividend 被除数
     * @param divisor  除数
     * @return 两个参数的商
     */
    public static Double divide(Double dividend, Double divisor) {
        if (dividend == null || divisor == null || !isValidDenominator(divisor)) {
            return null;
        }
        return divide(dividend, divisor, DEF_DIV_SCALE);
    }

    /**
     * 提供（相对）精确的除法运算。 当发生除不尽的情况时，由scale参数指定精度，以后的数字四舍五入。
     *
     * @param dividend 被除数
     * @param divisor  除数
     * @param scale    表示表示需要精确到小数点以后几位。
     * @return 两个参数的商
     */
    public static Double divide(Double dividend, Double divisor, Integer scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        if (dividend == null) {
            return null;
        }
        if (divisor == null) {
            return null;
        }
        BigDecimal b1 = new BigDecimal(Double.toString(dividend));
        BigDecimal b2 = new BigDecimal(Double.toString(divisor));
        return b1.divide(b2, scale, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 提供指定数值的（精确）小数位四舍五入处理。
     *
     * @param value 需要四舍五入的数字
     * @param scale 小数点后保留几位
     * @return 四舍五入后的结果
     */
    public static Double round(Double value, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        if (Objects.isNull(value)) {
            return null;
        }
        BigDecimal b = new BigDecimal(Double.toString(value));
        BigDecimal one = new BigDecimal("1");
        return b.divide(one, scale, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 计算给定数字的平方根。
     *
     * @param number 要计算平方根的数字
     * @return 返回给定数字的平方根, 如果number是null，则返回null
     * @throws IllegalArgumentException 如果给定的数字是负数
     */
    public static Double sqrt(Double number) {
        if (Objects.isNull(number)) {
            return null;
        }
        if (number < 0) {
            throw new IllegalArgumentException("The number cannot be negative for real square root");
        }
        return Math.sqrt(number);
    }

    /**
     * 计算给定double类型数据的平方。
     *
     * @param number 要计算平方的double类型数据
     * @return 返回给定数据的平方
     */
    public static Double square(Double number) {
        if (Objects.isNull(number)) {
            return null;
        }
        return mul(number, number);
    }

    /**
     * 判断一个double类型的值是否等于0（考虑浮点数的精度问题）
     *
     * @param value   要检查的double值
     * @param epsilon 允许的误差范围（阈值）
     * @return 如果值在[-epsilon, epsilon]范围内，则返回true，否则返回false
     */
    public static boolean isNotEqualZero(double value, double epsilon) {
        return Math.abs(value) > epsilon;
    }

    /**
     * 判断一个double类型的值是否不等于0（考虑浮点数的精度问题）
     *
     * @param value 要检查的值
     * @return 如果值在默认的阈值范围内，则返回true，否则返回false，如果这个值是null，则返回false
     */
    public static boolean isNotEqualZero(Double value) {
        if (Objects.isNull(value)) {
            return false;
        }
        return isNotEqualZero(value, EPSILON);
    }

    /**
     * 判断Double类型的分母是否合法（非null且不为0）
     *
     * @param denominator 分母
     * @return 如果分母合法则返回true，否则返回false
     */
    public static boolean isValidDenominator(Double denominator) {
        return Objects.nonNull(denominator) && isNotEqualZero(denominator);
    }
}

