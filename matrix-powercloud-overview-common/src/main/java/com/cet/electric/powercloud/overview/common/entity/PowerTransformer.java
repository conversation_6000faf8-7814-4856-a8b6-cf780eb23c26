package com.cet.electric.powercloud.overview.common.entity;
import com.cet.electric.modelservice.sdk.annotations.TableName;
import com.cet.electric.powercloud.common.model.device.BaseDeviceModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description: 变压器模型
 * @date 2022/9/27 13:44
 */

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("powertransformer")
public class PowerTransformer extends BaseDeviceModel {

    @JsonProperty("materialtype")
    private Integer materialType;
    @JsonProperty("materialtype$text")
    private String materialTypeText;
    @JsonProperty("nextoverhauldate")
    private Long nextOverhaulDate;
    @JsonProperty("noloadcurrent")
    private Double noLoadCurrent;
    @JsonProperty("noloadloss")
    private Double noLoadLoss;
    @JsonProperty("phasetype")
    private Integer phaseType;
    @JsonProperty("phasetype$text")
    private String phaseTypeText;
    @JsonProperty("ptratio")
    private String ptRatio;
    @JsonProperty("ratedcapacity")
    private Double ratedCapacity;
    @JsonProperty("ratedcurrent")
    private Double ratedCurrent;
    @JsonProperty("ratedloss")
    private Double ratedLoss;
    @JsonProperty("ratedvoltage")
    private Double ratedVoltage;
    @JsonProperty("shortcircuitimpedance")
    private Double shortCircuitImpedance;
    @JsonProperty("shortcircuitloss")
    private Double shortCircuitLoss;
    @JsonProperty("shortcircuitvoltage")
    private Double shortCircuitVoltage;
    @JsonProperty("status")
    private Integer status;
    @JsonProperty("status$text")
    private String statusText;
    @JsonProperty("coiltype")
    private Integer coilType;
    @JsonProperty("coiltype$text")
    private String coilTypeText;
    @JsonProperty("loadvarlosscoef")
    private Double loadVarLossCoeF;

    @JsonProperty("ratedpower")
    private Double ratedPower;
    @JsonProperty("inputvoltage")
    private String inputVoltage;
    @JsonProperty("outputvoltage")
    private String outputVoltage;
    @JsonProperty("insulationlevel")
    private String insulationLevel;
    @JsonProperty("protectiongrade")
    private String protectionGrade;
    @JsonProperty("shortcircuitcapacity")
    private Double shortCircuitCapacity;
    @JsonProperty("reactiveeconomicequivalent")
    private Double reactiveEconomicEquivalent;
    @JsonProperty("ratedcapacityofprimary")
    private Double ratedCapacityOfPrimary;
    @JsonProperty("ratedcapacityofsecondary")
    private Double ratedCapacityOfSecondary;
    @JsonProperty("ratedcapacityoftertiary")
    private Double ratedCapacityOfTertiary;
    @JsonProperty("ratedlossofprimary")
    private Double ratedLossOfPrimary;
    @JsonProperty("ratedlossofsecondary")
    private Double ratedLossOfSecondary;
    @JsonProperty("ratedlossoftertiary")
    private Double ratedLossOfTertiary;

}
