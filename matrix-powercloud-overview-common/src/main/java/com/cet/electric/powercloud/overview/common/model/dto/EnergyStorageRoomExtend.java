package com.cet.electric.powercloud.overview.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Copyright © 1993-2023 深圳市中电电力技术股份有限公司.All rights reserved.
 *
 * @author: zhangjie230121 张杰
 * @date: 2023-11-13  16:39
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EnergyStorageRoomExtend extends BaseRoomExtend {

    @ApiModelProperty("储能电站分类")
    private Integer sideType;

    @ApiModelProperty("总储电量")
    private Double totalEnergyStorageCapacity;

}
