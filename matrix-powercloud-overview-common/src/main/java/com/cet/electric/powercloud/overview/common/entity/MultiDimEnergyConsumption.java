package com.cet.electric.powercloud.overview.common.entity;

import com.cet.electric.baseconfig.common.base.BaseEntity;
import com.cet.electric.modelservice.sdk.annotations.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("multidimenergyconsumption")
public class MultiDimEnergyConsumption extends BaseEntity {


    private String dimtagids;

    private Integer aggregationcycle;

    private Long logtime;

    @JsonProperty("energytype")
    private Integer energyType;

    private String objectlabel;

    @JsonProperty("objectid")
    private Long objectId;

    private Double loss;

    private Double usage;

    @JsonProperty("timeshareperiod_identification")
    private String timeshareperiodIdentification;

    private Double total;

    private Long endtime;

}
