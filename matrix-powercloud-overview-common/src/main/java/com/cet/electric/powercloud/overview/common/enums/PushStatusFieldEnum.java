package com.cet.electric.powercloud.overview.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Copyright © 1993-2023 深圳市中电电力技术股份有限公司.All rights reserved.
 *
 * @author: zhangjie230121 张杰
 * @date: 2024-07-02  09:23
 * @description:
 */
@Getter
@AllArgsConstructor
public enum PushStatusFieldEnum {
    WEB(1, "sendwebstatus", "sendwebtime"),
    APP(2, "sendappstatus", "sendapptime"),
    SMS(3, "sendsmsstatus", "sendsmstime"),
    VOICE(4, "sendvoicestatus", "sendvoicetime");

    /**
     * 编码
     */
    private final int code;

    /**
     * 名称
     */
    private final String status;
    /**
     * 名称
     */
    private final String time;
}
