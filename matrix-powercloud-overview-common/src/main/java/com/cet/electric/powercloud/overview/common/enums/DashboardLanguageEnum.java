package com.cet.electric.powercloud.overview.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Copyright © 1993-2023 深圳市中电电力技术股份有限公司.All rights reserved.
 *
 * @author: zhangjie230121 张杰
 * @date: 2024-02-29  14:56
 * @description:
 */
@Getter
@AllArgsConstructor
public enum DashboardLanguageEnum {
    ERROR_O_NO_BUILDING(3001, "dashboard.error.o.noBuilding"),
    ERROR_O_NO_ROOM(3002, "dashboard.error.o.noRoom");

    private final Integer code;
    private final String msg;

    @Override
    public String toString() {
        return "DashboardLanguageEnum{" + "code=" + code + ", msg='" + msg + '\'' + '}';
    }
}
