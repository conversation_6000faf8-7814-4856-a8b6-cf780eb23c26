package com.cet.electric.powercloud.overview.common.entity;
import com.cet.electric.modelservice.sdk.annotations.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 能耗
 * @date 2022/10/18 15:34
 */
@Data
@TableName("energyconsumption")
public class EnergyConsumption {
    private Long id;
    @JsonProperty("aggregationcycle")
    private Integer aggregationCycle;
    @JsonProperty("endtime")
    private Long endTime;
    @JsonProperty("energytype")
    private Integer energyType;
    @JsonProperty("logtime")
    private Long logTime;
    private Double loss;
    @JsonProperty("objectlabel")
    private String objectLabel;
    @JsonProperty("objectid")
    private Long objectId;
    private Double usage;
    private Double total;
    @JsonProperty("timeshareperiod_identification")
    private String timeSharePeriodIdentification;
}
