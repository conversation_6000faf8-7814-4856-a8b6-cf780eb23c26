package com.cet.electric.powercloud.overview.common.constants;

import cn.hutool.core.collection.CollUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description: 表名常量
 * @date 2022/10/10 9:28
 */
public final class TableNameConstant {
    public static final String DISTRICT = "district";
    public static final String PROJECT = "project";
    public static final String BUILDING = "building";
    public static final String FLOOR = "floor";
    public static final String ROOM = "room";
    public static final String DEVICE = "device";
    public static final String EQUIPMENT_ACCOUNT = "equipmentaccount";
    public static final String POWER_DIS_CABINET = "powerdiscabinet";
    public static final String LINE_SEGMENT = "linesegment";
    public static final String LINE_SEGMENT_WITH_SWITCH = "linesegmentwithswitch";
    public static final String POWER_TRANSFORMER = "powertransformer";
    public static final String PEC_EVENT_EXTEND = "peceventextend";
    public static final String SYSTEM_EVENT = "systemevent";
    public static final String MESSAGE_PUSH_RECORD = "messagepushrecord";
    public static final String PIPE_LINE = "pipeline";
    public static final String MO_EVENT_COUNT = "mo_eventcount";
    public static final String POI_RECORD = "poirecord";
    public static final String CONTACT = "contact";
    public static final String ELECTRONIC_EQUIPMENT_TEMPLATE = "electronicequipmenttemplate";
    public static final String VOLTAGE_FLUCTUATION = "voltagefluctuation";
    public static final String VOLTAGE_FLUCTUATION_POI = "voltage_fluctuation_poi";
    public static final String UNBALANCE_DURATION = "unbalance_duration";
    public static final String UNBALANCE = "unbalance";
    public static final String UNBALANCE_POI = "unbalance_poi";
    public static final String LOAD_RATE = "loadrate";
    public static final String LOAD_RATE_DURATION = "loadrateduration";
    public static final String LOAD_RATE_POI = "loadrate_poi";
    public static final String QUANTITY_AGGREGATION_DATA = "quantityaggregationdata";
    public static final String PDI_STEADY_POI = "pdi_steadypoi";
    public static final String PQ_STEADY_DATA = "pqsteadydata";
    public static final String PQ_SET_POINT_EVENT = "pqsetpointevent";
    public static final String RO_PQ_SET_POINT_EVENT_COUNT = "ro_pqsetpointeventcount";
    public static final String PQ_VARIATION_EVENT = "pqvariationevent";
    public static final String RO_PQ_VARIATION_EVENT_COUNT = "ro_pqvariationeventcount";
    public static final String SWITCH_CABINET = "switchcabinet";
    public static final String METERING_CABINET = "meteringcabinet";
    public static final String EVALUATE_RECORD = "evaluaterecord";
    public static final String EVALUATE_ITEM = "evaluateitem";
    public static final String PM_WORK_SHEET = "pmworksheet";
    public static final String DEFECT_EVENT = "defectevent";
    public static final String PAGE_URL = "pageurl";
    public static final String PV_INVERTER = "pv_inverter";
    public static final String PV_ACCUMULATOR = "pv_accumulator";
    public static final String PV_ENERGY_CONTAINER = "pv_energycontainer";
    public static final String PV_METEOROGRAPH = "pv_meteorograph";
    public static final String PV_CHARGINGSTATION = "pv_chargingstation";
    public static final String PCS = "pcs";
    public static final String PV_WIND_TURBINES = "pv_windturbines";
    public static final String GATEWAY_DEVICE = "gatewaydevice";
    public static final String AIRCONDITIONER_STRATEGY_PERIOD = "airconditionerstrategyperiod";
    public static final String DAY_SHARE_SET = "dayshareset";
    public static final String DAY_SET = "dayset";
    public static final String PEC_ALARM_EXTEND = "pecalarmextend";
    public static final String PECEVENT_FILTER = "peceventextendfiltered";
    public static final String PECEVENT_CONVT = "peceventextendconvt";
    public static final String PEC_DEVICE_EXTEND = "pecdeviceextend";
    public static final String ALARM_CONV_DATA = "alarmconvdata";
    public static final String ALARM_CAUSE_CLASSIFY = "alarmcauseclassify";
    public static final String TEAM = "team";
    public static final String GENERAL_BREAKER_LOSS_POI = "generalbreakerloss_poi";
    public static final String BREAKER_LOSS = "breakerloss";
    public static final String TRIP_CURRENT_POI = "tripcurrent_poi";
    public static final String TRIP_CURRENT = "tripcurrent";
    public static final String BUSBAR_SECTION = "busbarsection";
    public static final String BUSBAR_CONNECTOR = "busbarconnector";
    /**
     * 表计
     */
    public static final String METER_DEVICE = "meterdevice";

    public static final String CIVICPIPE = "civicpipe";

    /**
     * 直流汇流箱 设备类型
     */
    public static final String PV_DC_COMBINER_BOX = "pv_dccombinerbox";

    /**
     * 空调机组 设备类型
     */
    public static final String COLD_WATER_MAIN_ENGINE = "coldwatermainengine";


    public static final String PV_DIVERGENCE_CONFIG = "pv_divergenceconfig";

    public static final String PV_COMPUTATION_CHANNEL = "pv_computationchannel";

    public static final String PV_STRING_MANAGEMENT = "pv_stringmanagement";

    public static final String CIRCUIT_BREAKER = "circuitbreaker";

    public static final List<String> OBJECT_LABELS = Arrays.asList(EQUIPMENT_ACCOUNT, PROJECT, ROOM);

    public static final Set<String> REQUIRE_AUTHENTICATION_MODELS = CollUtil.newHashSet(PROJECT, BUILDING, FLOOR, ROOM);

    private TableNameConstant() {
    }
}
