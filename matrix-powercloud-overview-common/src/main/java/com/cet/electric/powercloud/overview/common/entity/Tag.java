package com.cet.electric.powercloud.overview.common.entity;

import com.cet.electric.baseconfig.common.base.BaseEntity;
import com.cet.electric.modelservice.sdk.annotations.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("tag")
public class Tag extends BaseEntity {

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("是否删除")
    @JsonProperty("isdeleted")
    private Boolean isDeleted;

    @JsonProperty("tree_id")
    private String treeId;

}
