package com.cet.electric.powercloud.overview.core.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import com.cet.electric.baseconfig.common.base.BaseEntity;
import com.cet.electric.baseconfig.common.dao.DeviceTypeMapper;
import com.cet.electric.baseconfig.common.entity.DeviceType;
import com.cet.electric.baseconfig.common.entity.MeasuredBy;
import com.cet.electric.baseconfig.common.entity.QuantityObjectMap;
import com.cet.electric.baseconfig.sdk.entity.param.LabelAndId;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import com.cet.electric.matterhorn.devicedataservice.common.entity.realtime.DeviceDataIdLogicalId;
import com.cet.electric.matterhorn.devicedataservice.common.entity.realtime.MeasurePointData;
import com.cet.electric.modelservice.common.query.ModelQuery;
import com.cet.electric.modelservice.core.query.impl.ModelExtendService;
import com.cet.electric.modelservice.sdk.conditions.ListWithTotal;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryChainWrapper;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryWrapper;
import com.cet.electric.powercloud.common.client.DeviceDataExtendService;
import com.cet.electric.powercloud.common.client.model.SystemEvent;
import com.cet.electric.powercloud.common.enums.auth.AuthModelNodesEnum;
import com.cet.electric.powercloud.common.enums.common.AggregationCycleEnum;
import com.cet.electric.powercloud.common.enums.common.EnergyTypeEnum;
import com.cet.electric.powercloud.common.enums.common.QuantityDataEnum;
import com.cet.electric.powercloud.common.enums.common.SystemEventTypeEnum;
import com.cet.electric.powercloud.common.enums.device.RoomTypeEnum;
import com.cet.electric.powercloud.common.exception.BusinessException;
import com.cet.electric.powercloud.common.matrix.BaseConfigDeviceService;
import com.cet.electric.powercloud.common.matrix.BusinessAuthService;
import com.cet.electric.powercloud.common.model.device.*;
import com.cet.electric.powercloud.common.utils.*;
import com.cet.electric.powercloud.overview.common.constants.ModelConstant;
import com.cet.electric.powercloud.overview.common.constants.OverviewConstant;
import com.cet.electric.powercloud.overview.common.constants.SymbolConstant;
import com.cet.electric.powercloud.overview.common.constants.TableNameConstant;
import com.cet.electric.powercloud.overview.common.entity.*;
import com.cet.electric.powercloud.overview.common.enums.*;
import com.cet.electric.powercloud.overview.common.model.dto.*;
import com.cet.electric.powercloud.overview.common.model.request.QueryTheSameTermRequest;
import com.cet.electric.powercloud.overview.common.model.response.PvOverviewRoomInfoResponse;
import com.cet.electric.powercloud.overview.common.utils.DoubleUtil;
import com.cet.electric.powercloud.overview.core.mapper.*;
import com.cet.electric.powercloud.overview.core.service.*;
import com.cet.futureblue.i18n.LanguageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: lhw
 * @description:
 * @Date: 2024/10/29
 */
@Service
@Slf4j
public class OverviewServiceImpl implements OverviewService {

    @Autowired
    private BusinessAuthService businessAuthService;
    @Autowired
    private RoomService roomService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private MoEventCountMapper moEventCountMapper;
    @Autowired
    private EventService eventService;
    @Autowired
    private PhotovoltaicAlgorithmService photovoltaicAlgorithmService;
    @Autowired
    private DeviceTypeMapper deviceTypeMapper;
    @Autowired
    private PowerTransformerMapper powerTransformerMapper;
    @Autowired
    private DeviceRelationService deviceRelationService;
    @Autowired
    private DeviceDataExtendService deviceDataExtendService;
    @Autowired
    private ModelExtendService modelExtendService;
    @Autowired
    private SystemEventMapper systemEventMapper;
    @Autowired
    private EnergyConsumptionMapper energyConsumptionMapper;
    @Autowired
    private TransformerMonitoringService transformerMonitoringService;
    @Autowired
    private QuantityObjectService quantityObjectService;
    @Autowired
    private EventAnalysisService eventAnalysisService;
    @Autowired
    private EnergyStorageStationService energyStorageStationService;
    @Autowired
    private PvEnergyContainerMapper pvEnergyContainerMapper;
    @Autowired
    private BaseConfigDeviceService baseConfigDeviceService;
    @Autowired
    private NodeTreeService nodeTreeService;
    @Autowired
    private TagMapper tagMapper;
    @Autowired
    private RoomMapper roomMapper;
    @Autowired
    private Dim2EnergyMapper dim2EnergyMapper;
    @Autowired
    private MultiDimEnergyConsumptionMapper multiDimEnergyConsumptionMapper;
    @Autowired
    private PipelineMapper pipelineMapper;


    @Override
    public List<OverviewMapParam> queryOverviewMap(OverviewParam query) {
        List<OverviewMapParam> responseList = new ArrayList<>();
        List<Long> authProjectIds;
        if (Objects.isNull(query.getProjectId())) {
            authProjectIds = getAuthProjectIds();
        } else {
            authProjectIds = Collections.singletonList(query.getProjectId());
        }
        if (CollUtil.isEmpty(authProjectIds)) {
            return responseList;
        }
        // 查询多个项目下的权限房间
        List<Room> rooms = roomService.queryAuthRoomByProjectIds(authProjectIds, TableNameConstant.PROJECT);
        if (CollUtil.isEmpty(rooms)) {
            return responseList;
        }
        List<Long> authRoomIds = rooms.stream().map(Room::getId).collect(Collectors.toList());
        Map<Long, Integer> roomToStatusMap = eventService.getUnconfirmedEventRoomIds(query.getStartTime(), query.getEndTime(), authRoomIds);

        List<Integer> energyList = Arrays.asList(
                RoomTypeEnum.CHARGING_STATION.getCode(),
                RoomTypeEnum.PHOTOVOLTAIC_STATION.getCode(),
                RoomTypeEnum.ENERGY_STORAGE_STATION.getCode(),
                RoomTypeEnum.WIND_POWER_PLANT.getCode());
        List<Room> roomList = rooms.stream().filter(it -> energyList.contains(it.getRoomType())).collect(Collectors.toList());
        // 总装机容量(mW)
        Map<Long, Double> roomExtendMap = roomList.stream()
                .collect(Collectors.toMap(Room::getId, it ->
                        Optional.ofNullable(it.getTotalInstalledCapacity()).orElse(0d)));
        // 并网电压(kV)
        Map<Long, Double> roomGirdVoltageMap = roomList.stream()
                .collect(Collectors.toMap(Room::getId, it ->
                        Optional.ofNullable(it.getGirdVoltage()).orElse(0d)));
        // 装机功率(kW)
        Map<Long, Double> roomRatedPowerMap = roomList.stream()
                .collect(Collectors.toMap(Room::getId, it ->
                        Optional.ofNullable(it.getTotalRatedPower()).orElse(0d)));

        for (Room room : rooms) {
            OverviewMapParam overviewMapParam = new OverviewMapParam();
            overviewMapParam.setId(room.getId());
            overviewMapParam.setName(room.getName());
            overviewMapParam.setLongitude(room.getLongitude());
            overviewMapParam.setLatitude(room.getLatitude());
            overviewMapParam.setStatus(roomToStatusMap.getOrDefault(room.getId(), NodeEventProcessingStageEnum.CONFIRMED.getCode()));
            overviewMapParam.setAddress(room.getAddress());
            overviewMapParam.setRoomId(room.getId());
            if (CollUtil.isNotEmpty(roomList) && energyList.contains(room.getRoomType())) {
                overviewMapParam.setCapacity(Objects.equals(roomExtendMap.get(room.getId()), 0d) ? null : roomExtendMap.get(room.getId()));
                overviewMapParam.setGirdVoltage(Objects.equals(roomGirdVoltageMap.get(room.getId()), 0d) ? null : roomGirdVoltageMap.get(room.getId()));
                overviewMapParam.setTotalRatedPower(Objects.equals(roomRatedPowerMap.get(room.getId()), 0d) ? null : roomRatedPowerMap.get(room.getId()));
                overviewMapParam.setRoomType(room.getRoomType());
            }
            responseList.add(overviewMapParam);
        }
        return responseList;
    }

    @Override
    public PlatformOverviewResponse queryPlatformOverview(OverviewParam query) {
        PlatformOverviewResponse platformResponse = new PlatformOverviewResponse();
        List<Long> authProjectIds;
        if (Objects.isNull(query.getProjectId())) {
            authProjectIds = getAuthProjectIds();
        } else {
            authProjectIds = Collections.singletonList(query.getProjectId());
        }
        if (CollUtil.isEmpty(authProjectIds)) {
            return platformResponse;
        }
        // 项目总数
        platformResponse.setProjectCount(authProjectIds.size());
        QueryProjectParams queryProjectParams = new QueryProjectParams();
        queryProjectParams.setIds(authProjectIds);
        List<Project> authProjects = projectService.queryProject(queryProjectParams).getList();
        setSafeRunDays(authProjects, platformResponse);
        // 查询多个项目下的权限房间
        List<Room> roomList = roomService.queryAuthRoomByProjectIds(authProjectIds, TableNameConstant.PROJECT);
        if (CollUtil.isEmpty(roomList)) {
            return platformResponse;
        }
        platformResponse.setRoomCount(roomList.size());
        List<Long> authRoomIds = roomList.stream().map(Room::getId).collect(Collectors.toList());
        // 查询项目下设备
        List<EquipmentAccount> equipmentAccountList = nodeTreeService.getEquipmentAccounts(TableNameConstant.ROOM, authRoomIds);
        if (CollUtil.isEmpty(equipmentAccountList)) {
            return platformResponse;
        }
        // 变压器额定总容量
        setPropertyValue(platformResponse, equipmentAccountList);
        // 业务事件查询
        setProcessingState(query, platformResponse, equipmentAccountList);

        List<ModelInfo> modelInfoList = equipmentAccountList.stream()
                .map(self -> new ModelInfo(self.getId(), self.getModelLabel())).collect(Collectors.toList());
        List<MeasuredBy> measuredByList = deviceRelationService.queryDeviceRelationBatch(modelInfoList);
        if (CollUtil.isEmpty(measuredByList)) {
            return platformResponse;
        }
        // 设备在线率
        Map<String, List<Long>> labelToIdsMap = equipmentAccountList.stream()
                .collect(Collectors.groupingBy(EquipmentAccount::getModelLabel,
                        Collectors.mapping(EquipmentAccount::getId, Collectors.toList())));
        queryDeviceStatus(query, measuredByList, platformResponse, labelToIdsMap);

        return platformResponse;
    }

    @Override
    public ElectricAnalysisResponse queryElectricAnalysis(Long projectId) {
        ElectricAnalysisResponse response = new ElectricAnalysisResponse();
        List<Long> authProjectIds;
        if (Objects.isNull(projectId)) {
            authProjectIds = getAuthProjectIds();
        } else {
            authProjectIds = Collections.singletonList(projectId);
        }
        if (CollUtil.isEmpty(authProjectIds)) {
            return response;
        }

        long currentTime = System.currentTimeMillis();

        List<Long> dayTimeList = Arrays.asList(DateUtils.computeStartOfDay(currentTime), DateUtils.computeStartOfLastDay(currentTime));
        List<EnergyConsumption> energyDay = queryEnergy(authProjectIds, dayTimeList, AggregationCycleEnum.DAY.getId());
        if (CollUtil.isNotEmpty(energyDay)) {
            Map<Long, Double> energyDayMap = energyDay.stream()
                    .filter(it -> Objects.nonNull(it.getUsage()))
                    .collect(Collectors.groupingBy(EnergyConsumption::getLogTime, Collectors.summingDouble(EnergyConsumption::getUsage)));
            response.setTodayEnergyUsage(energyDayMap.get(DateUtils.computeStartOfDay(currentTime)));
            response.setYesterdayEnergyUsage(energyDayMap.get(DateUtils.computeStartOfLastDay(currentTime)));
        }

        List<Long> monthTimeList = Arrays.asList(DateUtils.computeStartOfMonth(currentTime), DateUtils.computeStartOfLastMonth(currentTime));
        List<EnergyConsumption> energyMonth = queryEnergy(authProjectIds, monthTimeList, AggregationCycleEnum.MONTH.getId());
        if (CollUtil.isNotEmpty(energyMonth)) {
            Map<Long, Double> energyMonthMap = energyMonth.stream()
                    .filter(it -> Objects.nonNull(it.getUsage()))
                    .collect(Collectors.groupingBy(EnergyConsumption::getLogTime, Collectors.summingDouble(EnergyConsumption::getUsage)));

            response.setMonthEnergyUsage(energyMonthMap.get(DateUtils.computeStartOfMonth(currentTime)));
            response.setLastMonthEnergyUsage(energyMonthMap.get(DateUtils.computeStartOfLastMonth(currentTime)));
        }
        return response;
    }

    @Override
    public List<DatalogValue> queryMonthUsageCurve(Long projectId,
                                                   Integer energyType,
                                                   Long startTime,
                                                   Long endTime) {
        long start = System.currentTimeMillis();
        List<DatalogValue> returnList = new ArrayList<>();
        List<Long> projects;
        if (Objects.isNull(projectId)) {
            List<Long> authProjectIds = getAuthProjectIds();
            if (CollUtil.isEmpty(authProjectIds)) {
                FillTimeAxisUtil.fillData(returnList, AggregationCycleEnum.DAY.getId(),
                        DateUtils.computeStartOfMonth(startTime),
                        DateUtils.computeStartOfNextMonth(startTime));
                return returnList;
            }
            projects = authProjectIds;
            log.debug("获取所有项目列表耗时:{}ms", System.currentTimeMillis() - start);
        } else {
            projects = Collections.singletonList(projectId);
        }
        start = System.currentTimeMillis();
        List<EnergyConsumption> dataList = new LambdaQueryChainWrapper<>(energyConsumptionMapper)
                .in(EnergyConsumption::getObjectId, projects)
                .eq(EnergyConsumption::getObjectLabel, TableNameConstant.PROJECT)
                .eq(EnergyConsumption::getAggregationCycle, AggregationCycleEnum.DAY.getId())
                .ge(EnergyConsumption::getLogTime, startTime)
                .lt(EnergyConsumption::getLogTime, endTime)
                .eq(EnergyConsumption::getEnergyType, energyType)
                .eq(EnergyConsumption::getTimeSharePeriodIdentification, CharSequenceUtil.EMPTY)
                .list();
        log.debug("获取项目的能耗信息耗时:{}ms", System.currentTimeMillis() - start);
        Map<Long, Double> energyDataMap = dataList.stream()
                .collect(Collectors.groupingBy(EnergyConsumption::getLogTime,
                        Collectors.summingDouble(EnergyConsumption::getUsage)));
        energyDataMap.forEach((time, value) -> {
            DatalogValue curvePointParam = new DatalogValue(time, value, 0);
            returnList.add(curvePointParam);
        });
        start = System.currentTimeMillis();
        FillTimeAxisUtil.fillData(returnList, AggregationCycleEnum.DAY.getId(),
                DateUtils.computeStartOfMonth(start),
                DateUtils.computeStartOfNextMonth(start));
        log.debug("填充时间耗时:{}ms", System.currentTimeMillis() - start);
        return returnList;

    }

    @Override
    public OverviewMapDetailParam queryOverviewRoomDetail(OverviewParam query) {
        ParamsAssert.notNull(query.getRoomId());
        OverviewMapDetailParam response = new OverviewMapDetailParam();
        Room room = roomService.queryRoomById(query.getRoomId());
        if (Objects.isNull(room)) {
            throw new BusinessException(LanguageUtil.getMessage(DashboardLanguageEnum.ERROR_O_NO_ROOM.getMsg()));
        }
        response.setRoomName(room.getName());
        response.setRoomType(room.getRoomType());
        response.setVoltageLevel(room.getVoltageLevelText());
        response.setAddress(room.getAddress());
        List<EquipmentAccount> equipmentAccountList = baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.ROOM, Collections.singletonList(query.getRoomId()));
        equipmentAccountList = equipmentAccountList.stream()
                .filter(it -> Objects.equals(it.getModelLabel(), TableNameConstant.POWER_TRANSFORMER))
                .collect(Collectors.toList());
        response.setTransformerNum(equipmentAccountList.size());
        if (CollUtil.isNotEmpty(equipmentAccountList)) {
            List<Long> deviceIds = equipmentAccountList.stream()
                    .map(EquipmentAccount::getId)
                    .collect(Collectors.toList());
            List<PowerTransformer> powerTransformers = powerTransformerMapper.selectBatchIds(deviceIds);
            response.setRatedCapacity(powerTransformers.stream()
                    .map(PowerTransformer::getRatedCapacity)
                    .reduce(0.0, Double::sum));
            List<Long> dataIds = Arrays.asList(QuantityDataEnum.APPARENT_POWER.getDataId().longValue(),
                    QuantityDataEnum.ACTIVE_POWER.getDataId().longValue(),
                    QuantityDataEnum.REACTIVE_POWER.getDataId().longValue());
            Map<String, MeasurePointData> realTimeDataMap = transformerMonitoringService.getRealDataToDeviceMap(deviceIds, dataIds);
            List<MeasurePointData> realTimeDataDTOS = new ArrayList<>(realTimeDataMap.values());
            Map<Integer, List<MeasurePointData>> map = realTimeDataDTOS.stream()
                    .filter(x -> x.getValue() != null)
                    .collect(Collectors.groupingBy(MeasurePointData::getDataId));
            if (CollUtil.isNotEmpty(map.get(QuantityDataEnum.ACTIVE_POWER.getDataId()))) {
                response.setActivePower(map.get(QuantityDataEnum.ACTIVE_POWER.getDataId())
                        .stream()
                        .map(MeasurePointData::getValue)
                        .reduce(0.0, Double::sum));
            }
            if (CollUtil.isNotEmpty(map.get(QuantityDataEnum.REACTIVE_POWER.getDataId()))) {
                response.setReactivePower(map.get(QuantityDataEnum.REACTIVE_POWER.getDataId())
                        .stream()
                        .map(MeasurePointData::getValue)
                        .reduce(0.0, Double::sum));
            }
            // 负载率
            Map<Long, Double> doubleMap = new HashMap<>(deviceIds.size());
            for (Map.Entry<String, MeasurePointData> entry : realTimeDataMap.entrySet()) {
                if (entry.getValue().getDataId().equals(QuantityDataEnum.APPARENT_POWER.getDataId())) {
                    String[] locationSplit = entry.getKey().split(SymbolConstant.UNDER_SCORE);
                    doubleMap.put(Long.valueOf(locationSplit[0]), entry.getValue().getValue());
                }
            }
            if (doubleMap.size() > 0) {
                response.setLoadRate(transformerMonitoringService.queryLoadFactor(deviceIds, doubleMap));
            }
        }
        // 温度、湿度
        List<QuantityObjectMap> quantityObjectMaps = quantityObjectService.getQuantityObjectMapByDataId(
                TableNameConstant.ROOM, query.getRoomId(),
                Arrays.asList(QuantityDataEnum.TEMPERATURE.getDataId().longValue(), QuantityDataEnum.HUMIDITY.getDataId().longValue()));
        List<DeviceDataIdLogicalId> pecRealDateQueryDTOS = new ArrayList<>();
        quantityObjectMaps.forEach(x ->
        {
            DeviceDataIdLogicalId deviceDataIdLogicalId = new DeviceDataIdLogicalId();
            deviceDataIdLogicalId.setDataId(x.getDataId());
            deviceDataIdLogicalId.setLogicalId(1);
            deviceDataIdLogicalId.setDeviceId(x.getDeviceId());
            pecRealDateQueryDTOS.add(deviceDataIdLogicalId);
        });
        List<MeasurePointData> result = deviceDataExtendService.batchRealTimeData(pecRealDateQueryDTOS);
        for (MeasurePointData dataDTO : result) {
            if (dataDTO.getDataId().equals(QuantityDataEnum.TEMPERATURE.getDataId())) {
                response.setTemperature(dataDTO.getValue());
            }
            if (dataDTO.getDataId().equals(QuantityDataEnum.HUMIDITY.getDataId())) {
                response.setHumidity(dataDTO.getValue());
            }
        }
        // 事件
        EventAnalysisParams params = new EventAnalysisParams();
        ConvertUtil.copyProperties(query, params);
        params.setModelLabel(TableNameConstant.ROOM);
        params.setModelIds(Collections.singletonList(query.getRoomId()));
        EventOverviewParam eventOverviewParam = eventAnalysisService.queryEventMatchOverview(params);
        response.setEvent(eventOverviewParam);
        return response;
    }

    @Override
    public PvOverviewRoomInfoResponse overviewRoomInfo(PvOverviewRoomInfo request) {
        PvOverviewRoomInfoResponse roomInfoResponse = new PvOverviewRoomInfoResponse();
        ConvertUtil.copyProperties(request, roomInfoResponse);
        // 查询设备
        List<EquipmentAccount> deviceList = baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.ROOM, Collections.singletonList(request.getRoomId()));
        if (Objects.equals(request.getRoomType(), RoomTypeEnum.PHOTOVOLTAIC_STATION.getCode())) {
            // 光伏电站
            deviceList = deviceList.stream()
                    .filter(it -> Objects.equals(TableNameConstant.PV_INVERTER, it.getModelLabel())).collect(Collectors.toList());

        } else if (Objects.equals(request.getRoomType(), RoomTypeEnum.ENERGY_STORAGE_STATION.getCode())) {
            // 储能场站
            deviceList = deviceList.stream()
                    .filter(it -> Objects.equals(TableNameConstant.PCS, it.getModelLabel()) || Objects.equals(TableNameConstant.PV_ACCUMULATOR, it.getModelLabel()))
                    .collect(Collectors.toList());
        } else if (Objects.equals(request.getRoomType(), RoomTypeEnum.WIND_POWER_PLANT.getCode())) {
            // 风电机组设备
            deviceList = deviceList.stream()
                    .filter(it -> Objects.equals(TableNameConstant.PV_WIND_TURBINES, it.getModelLabel())).collect(Collectors.toList());
        } else {
            // 充电桩
            deviceList = deviceList.stream()
                    .filter(it -> Objects.equals(TableNameConstant.PV_CHARGINGSTATION, it.getModelLabel())).collect(Collectors.toList());
        }
        List<MeasuredBy> measuredByList = deviceRelationService.queryDeviceRelationBatch(deviceList.stream()
                .map(it -> new ModelInfo(it.getId(), it.getModelLabel()))
                .collect(Collectors.toList()));
        if (CollUtil.isEmpty(measuredByList)) {
            return roomInfoResponse;
        }
        // 光伏电站 和 风电场站
        if (Objects.equals(request.getRoomType(), RoomTypeEnum.PHOTOVOLTAIC_STATION.getCode())
                || Objects.equals(request.getRoomType(), RoomTypeEnum.WIND_POWER_PLANT.getCode())) {
            // 光伏电站 等效发电小时
            roomInfoResponse.setValue(queryEquivalentOutputHour(measuredByList, request.getCapacity()));
            // 设备在线
            setDeviceStatus(roomInfoResponse, measuredByList);
            // 离线和未关联的都算
            roomInfoResponse.setOfflineCount(deviceList.size() - roomInfoResponse.getOnlineCount());
        } else if (Objects.equals(request.getRoomType(), RoomTypeEnum.ENERGY_STORAGE_STATION.getCode())) {
            // 储能场站  等效利用系数
            List<EnergyStorageStationRankingDTO> storageList = energyStorageStationService.energyStorageStationEAFRanking(null, request.getRoomId());
            if (CollUtil.isNotEmpty(storageList)) {
                roomInfoResponse.setValue(storageList.get(0).getValue());
            }
            // 储能集装箱在线和离线
            setEnergyContainerStatus(request, roomInfoResponse, measuredByList);
        } else {
            // 充电桩设备在线
            setDeviceStatus(roomInfoResponse, measuredByList);
            // 离线和未关联的都算
            roomInfoResponse.setOfflineCount(deviceList.size() - roomInfoResponse.getOnlineCount());
        }
        // 本月报警事件 数量
        eventCounts(request, roomInfoResponse);
        // 总发电量; 日发电量;总有功功率
        if (!Objects.equals(request.getRoomType(), RoomTypeEnum.CHARGING_STATION.getCode())) {
            setPecRealDate(roomInfoResponse, measuredByList);
        }
        return roomInfoResponse;
    }

    @Override
    public List<PeakVallyParams> queryMonthPeakVallyDistribute(Long modelId, Long startTime, Long endTime) {
        List<Long> authProjectIds;
        if (Objects.isNull(modelId)) {
            authProjectIds = getAuthProjectIds();
        } else {
            authProjectIds = Collections.singletonList(modelId);
        }
        if (CollUtil.isEmpty(authProjectIds)) {
            return Collections.emptyList();
        }
        long lastMonth = DateUtils.computeStartOfLastMonth(startTime);
        List<EnergyConsumption> energyConsumptionList = new LambdaQueryChainWrapper<>(energyConsumptionMapper)
                .in(EnergyConsumption::getObjectId, authProjectIds)
                .eq(EnergyConsumption::getObjectLabel, TableNameConstant.PROJECT)
                .eq(EnergyConsumption::getAggregationCycle, AggregationCycleEnum.MONTH.getId())
                .ge(EnergyConsumption::getLogTime, lastMonth)
                .lt(EnergyConsumption::getLogTime, endTime)
                .list()
                .stream()
                //峰、谷、平此处值不会空
                .filter(self -> CharSequenceUtil.isNotEmpty(self.getTimeSharePeriodIdentification()))
                //过滤掉usage为空的脏数据
                .filter(self -> Objects.nonNull(self.getUsage()))
                .collect(Collectors.toList());

        return supplyMonthPeakVallyDistributeData(lastMonth, startTime, energyConsumptionList);
    }

    @Override
    public List<QueryItemizeDataDTO> queryTheSameTerm(QueryTheSameTermRequest request) {
        List<Long> authProjectIds;
        if (Objects.isNull(request.getObjectId())) {
            authProjectIds = getAuthProjectIds();
        } else {
            authProjectIds = Collections.singletonList(request.getObjectId());
        }
        Long startTime = request.getStartTime();
        //月 - 上月份同期
        Long monthLogTime = DateUtils.computeStartOfLastMonth(startTime);


        List<QueryItemizeDataDTO> returnList = new ArrayList<>();
        //获取能耗分项类型
        List<Tag> subItemParams = tagMapper.selectList(new LambdaQueryWrapper<Tag>().in(Tag::getId, request.getSubItems()));
        Map<Long, Tag> tagMap = subItemParams.stream()
                .collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        List<EquipmentAccount> equipmentAccountList = baseConfigDeviceService.getEquipmentAccounts(request.getObjectLabel(), authProjectIds, AuthUtil.getTenantId(), AuthUtil.getUserId());
        if (CollUtil.isEmpty(equipmentAccountList)) {
            subItemParams.forEach(sub -> returnList.add(new QueryItemizeDataDTO(sub.getName(), sub.getId(), null, null, null)));
            return returnList;
        }
        //分项能耗类型分组
        List<Dim2Energy> dim2Energies = this.queryDim2EnergyByNodes(equipmentAccountList.stream()
                        .map(e -> new LabelAndId(e.getModelLabel(), e.getId()))
                        .collect(Collectors.toList()),
                // 数据库中的energytype都是空，不传空查不出
                null);
        Map<Long, List<Dim2Energy>> listMap = dim2Energies.stream()
                .collect(Collectors.groupingBy(Dim2Energy::getTagid));


        listMap.forEach((key, values) -> {
            //查询分项能耗值
            List<MultiDimEnergyConsumption> dataList = getQuantityAggregationDataList(monthLogTime, startTime, values);
            if (CollUtil.isEmpty(dataList)) {
                return;
            }
            //安装周期分组 最多3组 ,日月年
            Map<Integer, List<MultiDimEnergyConsumption>> aggregationListMap = dataList.stream()
                    .collect(Collectors.groupingBy(MultiDimEnergyConsumption::getAggregationcycle));
            aggregationListMap.forEach((aggregationCycleKey, entryValue) -> {
                //分组聚合求和
                Map<Long, Double> logTimeValueMap = dataList.stream()
                        .collect(Collectors.groupingBy(MultiDimEnergyConsumption::getLogtime,
                                Collectors.summingDouble(MultiDimEnergyConsumption::getTotal)));
                Tag tag = tagMap.get(key);
                if (Objects.isNull(tag)) {
                    return;
                }
                //时间分组 默认2组 ,当前时间与同比时间
                QueryItemizeDataDTO queryItemizeDataDTO = new QueryItemizeDataDTO();
                queryItemizeDataDTO.setId(tag.getId());
                queryItemizeDataDTO.setName(tag.getName());
                logTimeValueMap.forEach((logTime, value) -> {
                    if (Objects.equals(monthLogTime, logTime)) {
                        queryItemizeDataDTO.setLastValue(value);
                    } else {
                        queryItemizeDataDTO.setValue(value);
                    }
                });
                Double periodOverPeriodChange = calculatePeriodOverPeriodChange(queryItemizeDataDTO.getLastValue(), queryItemizeDataDTO.getValue());
                queryItemizeDataDTO.setPeriodOverPeriodChange(periodOverPeriodChange);
                returnList.add(queryItemizeDataDTO);
            });
        });
        Map<Long, QueryItemizeDataDTO> returnMap = returnList.stream()
                .collect(Collectors.toMap(QueryItemizeDataDTO::getId,
                        Function.identity(),
                        (v1, v2) -> v1));

        //遍历枚举值
        for (Tag itemParams : subItemParams) {
            QueryItemizeDataDTO queryItemizeDataDTO = returnMap.get(itemParams.getId());
            if (Objects.isNull(queryItemizeDataDTO)) {
                returnList.add(new QueryItemizeDataDTO(itemParams.getName(), itemParams.getId(), null, null, null));
            }
        }
        return returnList;
    }

    @Override
    public List<EnergyTypeDTO> queryEnergyTypes(Long projectId) {

        List<Long> authProjects;
        if(Objects.isNull(projectId)){
            authProjects = getAuthProjectIds(AuthUtil.getUserId(), AuthUtil.getTenantId());
        }else {
            authProjects = Collections.singletonList(projectId);
        }

        List<EnergyTypeDTO> displayResponse = new ArrayList<>();
        EnergyTypeDTO energyType = new EnergyTypeDTO();
        energyType.setEnergyType(EnergyTypeEnum.ELECTRIC.getType());
        energyType.setName(EnergyTypeEnum.getI18nName(EnergyTypeEnum.ELECTRIC.getType()));
        displayResponse.add(energyType);

        if(CollUtil.isEmpty(authProjects)){
            return displayResponse;
        }

        List<Long> pipeLineIds = baseConfigDeviceService.getEquipmentAccounts(ModelConstant.PROJECT, authProjects)
                .stream()
                .filter(it -> Objects.equals(it.getModelLabel(), ModelConstant.PIPELINE))
                .map(EquipmentAccount::getId)
                .collect(Collectors.toList());

        if(CollUtil.isEmpty(pipeLineIds)){
            return displayResponse;
        }

        List<Pipeline> pipelines = new LambdaQueryChainWrapper<>(pipelineMapper)
                .in(Pipeline::getId, pipeLineIds)
                .in(Pipeline::getEnergyType, Arrays.asList(EnergyTypeEnum.TAP_WATER.getType(), EnergyTypeEnum.NATURAL_GAS.getType()))
                .list();
        if (CollUtil.isEmpty(pipelines)) {
            return displayResponse;
        }

        boolean hasTapWater = pipelines.stream()
                .anyMatch(it -> Objects.equals(it.getEnergyType(), EnergyTypeEnum.TAP_WATER.getType()));
        if(hasTapWater){
            EnergyTypeDTO energyTypeDTO = new EnergyTypeDTO();
            energyTypeDTO.setEnergyType(EnergyTypeEnum.TAP_WATER.getType());
            energyTypeDTO.setName(EnergyTypeEnum.getI18nName(EnergyTypeEnum.TAP_WATER.getType()));
            displayResponse.add(energyTypeDTO);
        }

        boolean hasNaturalGas = pipelines.stream()
                .anyMatch(it -> Objects.equals(it.getEnergyType(), EnergyTypeEnum.NATURAL_GAS.getType()));
        if(hasNaturalGas){
            EnergyTypeDTO energyTypeDTO = new EnergyTypeDTO();
            energyTypeDTO.setEnergyType(EnergyTypeEnum.NATURAL_GAS.getType());
            energyTypeDTO.setName(EnergyTypeEnum.getI18nName(EnergyTypeEnum.NATURAL_GAS.getType()));
            displayResponse.add(energyTypeDTO);
        }
        return displayResponse;
    }

    private List<Long> getAuthProjectIds(Long userId, Long tenantId) {
        return businessAuthService.getAuthNodeIds(AuthModelNodesEnum.PROJECT, userId, tenantId);
    }

    @Override
    public List<EventDistributeParam> queryEventTypeDistribute(String modelLabel,
                                                               Long modelId,
                                                               Long startTime,
                                                               Long endTime,
                                                               Integer aggregationCycle) {
        EventAnalysisParams params = new EventAnalysisParams();
        params.setStartTime(startTime);
        params.setEndTime(endTime);
        params.setAggregationCycle(aggregationCycle);
        List<EventDistributeParam> responses = new ArrayList<>();
        if (TableNameConstant.REQUIRE_AUTHENTICATION_MODELS.contains(modelLabel)) {
            List<Room> roomList = new LinkedList<>();
            if (Objects.isNull(modelId)) {
                //只有project查询，id可能为空
                List<Long> authProjectIds = getAuthProjectIds();
                for (Long id : authProjectIds) {
                    List<Room> rooms = getRoomList(modelLabel, id, Collections.emptyList());
                    if (CollectionUtils.isNotEmpty(rooms)) {
                        roomList.addAll(rooms);
                    }
                }
            } else {
                roomList = getRoomList(modelLabel, modelId, Collections.emptyList());
            }
            List<Long> roomIds = roomList.stream().map(Room::getId).collect(Collectors.toList());
            if (CollUtil.isEmpty(roomIds)) {
                return responses;
            }
            params.setModelLabel(TableNameConstant.ROOM);
            params.setModelIds(roomIds);
        } else {
            params.setModelLabel(modelLabel);
            params.setModelIds(Collections.singletonList(modelId));
        }
        List<MoEventCount> events = queryEventCounts(params);
        Map<Integer, List<MoEventCount>> map = events.stream()
                .collect(Collectors.groupingBy(MoEventCount::getEventType));
        for (Map.Entry<Integer, List<MoEventCount>> entry : map.entrySet()) {
            EventDistributeParam response = new EventDistributeParam();
            response.setName(SystemEventTypeEnum.of(entry.getKey()));
            response.setNum(entry.getValue().stream().mapToLong(MoEventCount::getCount).sum());
            responses.add(response);
        }
        return responses.stream()
                .sorted(Comparator.comparingLong(EventDistributeParam::getNum).reversed())
                .limit(5)
                .collect(Collectors.toList());
    }

    @Override
    public EventTimelinessDTO queryTimeliness(String modelLabel,
                                              Long modelId,
                                              Long startTime, Long endTime) {
        List<Long> authProjectIds;
        if (Objects.isNull(modelId)) {
            authProjectIds = getAuthProjectIds();
            if (CollUtil.isEmpty(authProjectIds)) {
                return EventTimelinessDTO.init();
            }
        } else {
            authProjectIds = Collections.singletonList(modelId);
        }
        List<EquipmentAccount> equipmentList = nodeTreeService.getEquipmentAccounts(TableNameConstant.PROJECT, authProjectIds);
        if (CollUtil.isEmpty(equipmentList)) {
            return EventTimelinessDTO.init();
        }
        Map<String, List<Long>> labelToIdsMap = equipmentList.stream()
                .collect(
                        Collectors.groupingBy(EquipmentAccount::getModelLabel,
                                Collectors.mapping(EquipmentAccount::getId, Collectors.toList())));

        return getTimeliness(startTime, endTime, labelToIdsMap);
    }

    private void setPecRealDate(PvOverviewRoomInfoResponse roomInfoResponse, List<MeasuredBy> measuredByList) {
        List<DeviceDataIdLogicalId> pecRealDateQuery = new ArrayList<>();

        if (Objects.equals(roomInfoResponse.getRoomType(), RoomTypeEnum.ENERGY_STORAGE_STATION.getCode())) {
            // 总有功功率 ;总充电量 ;总放电量
            List<Long> dataIds = Arrays.asList(
                    QuantityDataEnum.ACTIVE_POWER.getDataId().longValue(),
                    QuantityDataEnum.ENERGY_STORAGE_TOTAL_CHARGE.getDataId().longValue(),
                    QuantityDataEnum.ENERGY_STORAGE_TOTAL_DISCHARGE.getDataId().longValue()
            );
            Map<Integer, String> measuredbyToLabelMap = CollStreamUtil.toMap(measuredByList, MeasuredBy::getMeasuredBy, MeasuredBy::getMonitoredLabel);
            // 查询测点实时数据
            List<DeviceDataIdLogicalId> pecRealDateQueryDTOS = getPecRealDateQueryDTOS(measuredByList, dataIds);
            // 各实时数据根据设备类别和测点Id分组
            Map<String, Map<Integer, List<MeasurePointData>>> labelAndDataIdToValuesMap = deviceDataExtendService.batchRealTimeData(pecRealDateQueryDTOS)
                    .stream()
                    .filter(item -> Objects.nonNull(item.getValue()))
                    .collect(Collectors.groupingBy(item -> measuredbyToLabelMap.get(item.getDeviceId()),
                            Collectors.groupingBy(MeasurePointData::getDataId,
                                    Collectors.toList())));
            // 设置充放电功率
            Optional.ofNullable(labelAndDataIdToValuesMap.get(TableNameConstant.PCS))
                    .map(map -> map.get(QuantityDataEnum.ACTIVE_POWER.getDataId()))
                    .ifPresent(doubles ->
                            roomInfoResponse.setCurrentPower(CommonUtil.calculateDoublesSum(CollStreamUtil.toList(doubles, MeasurePointData::getValue))));
            // 设置总充电量和总放电量
            Optional.ofNullable(labelAndDataIdToValuesMap.get(TableNameConstant.PV_ACCUMULATOR))
                    .map(map -> map.get(QuantityDataEnum.ENERGY_STORAGE_TOTAL_CHARGE.getDataId()))
                    .ifPresent(doubles ->
                            roomInfoResponse.setChargingAmount(CommonUtil.calculateDoublesSum(CollStreamUtil.toList(doubles, MeasurePointData::getValue))));
            Optional.ofNullable(labelAndDataIdToValuesMap.get(TableNameConstant.PV_ACCUMULATOR))
                    .map(map -> map.get(QuantityDataEnum.ENERGY_STORAGE_TOTAL_DISCHARGE.getDataId()))
                    .ifPresent(doubles ->
                            roomInfoResponse.setDischargingAmount(CommonUtil.calculateDoublesSum(CollStreamUtil.toList(doubles, MeasurePointData::getValue))));
        } else {
            // 总发电量; 日发电量;总有功功率
            for (MeasuredBy measuredBy : measuredByList) {
                pecRealDateQuery.add(getDeviceDataIdLogicalId(QuantityDataEnum.DAILY_POWER_OUTPUT.getDataId(), measuredBy.getMeasuredBy()));
                pecRealDateQuery.add(getDeviceDataIdLogicalId(QuantityDataEnum.ACTIVE_POWER.getDataId(), measuredBy.getMeasuredBy()));
                pecRealDateQuery.add(getDeviceDataIdLogicalId(QuantityDataEnum.TOTAL_POWER_OUTPUT.getDataId(), measuredBy.getMeasuredBy()));
            }

            List<MeasurePointData> pecRealTimeDataList = deviceDataExtendService.batchRealTimeData(pecRealDateQuery);
            //有功功率统计
            List<MeasurePointData> currentPowerList = pecRealTimeDataList.stream()
                    .filter(it -> Objects.nonNull(it.getValue()))
                    .filter(it -> Objects.equals(it.getDataId(), QuantityDataEnum.ACTIVE_POWER.getDataId()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(currentPowerList)) {
                roomInfoResponse.setCurrentPower(currentPowerList.stream().mapToDouble(MeasurePointData::getValue).sum());
            }
            //日发电量统计
            List<MeasurePointData> outputDataList = pecRealTimeDataList.stream()
                    .filter(it -> Objects.nonNull(it.getValue()))
                    .filter(it -> Objects.equals(it.getDataId(), QuantityDataEnum.DAILY_POWER_OUTPUT.getDataId()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(outputDataList)) {
                Double outputValue = outputDataList.stream().mapToDouble(MeasurePointData::getValue).sum();
                roomInfoResponse.setDailyPowerOutput(outputValue);
            }
            //总发电量统计
            List<MeasurePointData> totalPowerOutputList = pecRealTimeDataList.stream()
                    .filter(it -> Objects.nonNull(it.getValue()))
                    .filter(it -> Objects.equals(it.getDataId(), QuantityDataEnum.TOTAL_POWER_OUTPUT.getDataId()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(totalPowerOutputList)) {
                double totalPowerOutput = totalPowerOutputList.stream()
                        .filter(it -> Objects.nonNull(it.getValue()))
                        .mapToDouble(MeasurePointData::getValue).sum();
                roomInfoResponse.setTotalPowerOutput(totalPowerOutput);
            }
        }
    }

    private DeviceDataIdLogicalId getDeviceDataIdLogicalId(Integer dataId, Integer deviceId) {
        DeviceDataIdLogicalId deviceDataIdLogicalId = new DeviceDataIdLogicalId();
        deviceDataIdLogicalId.setDataId(dataId);
        deviceDataIdLogicalId.setLogicalId(1);
        deviceDataIdLogicalId.setDeviceId(deviceId);
        return deviceDataIdLogicalId;
    }

    private List<DeviceDataIdLogicalId> getPecRealDateQueryDTOS(Collection<MeasuredBy> measuredByList, Collection<Long> dataIds) {
        // 查询测点实时数据
        List<DeviceDataIdLogicalId> pecRealDateQueryDTOS = new ArrayList<>(measuredByList.size() * dataIds.size());
        for (MeasuredBy measuredBy : measuredByList) {
            for (Long dataId : dataIds) {
                DeviceDataIdLogicalId deviceDataIdLogicalId = new DeviceDataIdLogicalId();
                deviceDataIdLogicalId.setDataId(dataId.intValue());
                deviceDataIdLogicalId.setLogicalId(1);
                deviceDataIdLogicalId.setDeviceId(measuredBy.getMeasuredBy());
                pecRealDateQueryDTOS.add(deviceDataIdLogicalId);
            }
        }
        return pecRealDateQueryDTOS;
    }


    private void eventCounts(PvOverviewRoomInfo request, PvOverviewRoomInfoResponse roomInfoResponse) {
        List<MoEventCount> eventCounts = new LambdaQueryChainWrapper<>(moEventCountMapper)
                .eq(MoEventCount::getAggregationCycle, AggregationCycleEnum.MONTH.getId())
                .eq(MoEventCount::getEventLabel, TableNameConstant.SYSTEM_EVENT)
                // 本月 1号0点时间戳
                .eq(MoEventCount::getLogTime, DateUtils.computeStartOfMonth(System.currentTimeMillis()))
                .eq(MoEventCount::getMonitoredLabel, TableNameConstant.ROOM)
                .eq(MoEventCount::getMonitoredId, request.getRoomId())
                .list();
        if (CollUtil.isNotEmpty(eventCounts)) {
            long total = eventCounts.stream()
                    .filter(event -> Objects.nonNull(event.getCount()))
                    .mapToLong(MoEventCount::getCount)
                    .sum();
            roomInfoResponse.setEventCount(total);
        }
    }

    private void setEnergyContainerStatus(PvOverviewRoomInfo request, PvOverviewRoomInfoResponse roomInfoResponse, List<MeasuredBy> measuredByList) {
        // 获取 储能集装箱ids
        List<Long> energyContainerIds = baseConfigDeviceService.getEquipmentAccounts(TableNameConstant.ROOM, Collections.singletonList(request.getRoomId()))
                .stream()
                .filter(it -> Objects.equals(it.getModelLabel(), TableNameConstant.PV_ENERGY_CONTAINER))
                .map(EquipmentAccount::getId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(energyContainerIds)) {
            // 获取 储能集装箱 下对应的蓄电池和PCS 设备
            List<PvEnergyContainer> pvEnergyContainers = new LambdaQueryChainWrapper<>(pvEnergyContainerMapper)
                    .in(PvEnergyContainer::getId, energyContainerIds)
                    .join(j -> j.joinModel(TableNameConstant.PCS))
                    .join(j -> j.joinModel(TableNameConstant.PV_ACCUMULATOR))
                    .list();
            // 设备在线状态map
            Map<String, Integer> deviceStatusMap = getEnergyContainer(measuredByList);
            int onCount = 0;
            for (PvEnergyContainer pvEnergyContainer : pvEnergyContainers) {
                long pcsCount = 0;
                long accumulatorCount = 0;
                List<PCS> pcsList = pvEnergyContainer.getPcsList();
                if (CollUtil.isNotEmpty(pcsList)) {
                    pcsCount = pcsList.stream()
                            .map(pcs -> TableNameConstant.PCS + SymbolConstant.UNDER_SCORE + pcs.getId())
                            .map(deviceStatusMap::get)
                            .filter(Objects::isNull)
                            .count();
                }
                List<PvAccumulator> pvAccumulatorList = pvEnergyContainer.getPvAccumulatorList();
                if (CollUtil.isNotEmpty(pvAccumulatorList)) {
                    accumulatorCount = pvAccumulatorList.stream()
                            .map(accumulator -> TableNameConstant.PV_ACCUMULATOR + SymbolConstant.UNDER_SCORE + accumulator.getId())
                            .map(deviceStatusMap::get)
                            .filter(Objects::isNull)
                            .count();
                }
                if (Objects.equals(0L, pcsCount + accumulatorCount)) {
                    onCount++;
                }
            }
            roomInfoResponse.setOnlineCount(onCount);
            roomInfoResponse.setOfflineCount(pvEnergyContainers.size() - onCount);
        }
    }

    private Map<String, Integer> getEnergyContainer(List<MeasuredBy> measuredByList) {
        Map<String, Integer> map = new HashMap();
        List<Integer> pecDeviceIds = measuredByList.stream().map(MeasuredBy::getMeasuredBy).collect(Collectors.toList());
        Map<Integer, Integer> onlineMap = deviceDataExtendService.queryDeviceStatus(pecDeviceIds);
        for (MeasuredBy measured : measuredByList) {
            Integer status = onlineMap.get(measured.getMeasuredBy());
            if (Objects.nonNull(status) && PecDeviceStatusEnum.isNormal(status)) {
                String key = measured.getMonitoredLabel() + SymbolConstant.UNDER_SCORE + measured.getMonitoredId();
                map.put(key, 1);
            }
        }
        return map;
    }

    private void setDeviceStatus(PvOverviewRoomInfoResponse roomInfoResponse, List<MeasuredBy> measuredByList) {
        int onlineCount = 0;
        List<Integer> pecDeviceIds = measuredByList.stream().map(MeasuredBy::getMeasuredBy).collect(Collectors.toList());
        Map<Integer, Integer> onlineMap = deviceDataExtendService.queryDeviceStatus(pecDeviceIds);
        for (MeasuredBy measured : measuredByList) {
            Integer status = onlineMap.get(measured.getMeasuredBy());
            if (Objects.nonNull(status) && PecDeviceStatusEnum.isNormal(status)) {
                onlineCount++;
            }
        }
        roomInfoResponse.setOnlineCount(onlineCount);
    }

    private Double queryEquivalentOutputHour(List<MeasuredBy> measuredByList, Double capacity) {
        List<DeviceDataIdLogicalId> pecRealDateQueryDTOS = measuredByList.stream()
                .map(measuredBy -> {
                    DeviceDataIdLogicalId deviceDataIdLogicalId = new DeviceDataIdLogicalId();
                    deviceDataIdLogicalId.setDataId(QuantityDataEnum.DAILY_POWER_OUTPUT.getDataId());
                    deviceDataIdLogicalId.setLogicalId(1);
                    deviceDataIdLogicalId.setDeviceId(measuredBy.getMeasuredBy());
                    return deviceDataIdLogicalId;
                })
                .collect(Collectors.toList());
        double sum = deviceDataExtendService.batchRealTimeData(pecRealDateQueryDTOS).stream()
                .filter(it -> Objects.equals(it.getDataId(), QuantityDataEnum.DAILY_POWER_OUTPUT.getDataId()))
                .filter(it -> Objects.nonNull(it.getValue()))
                .mapToDouble(MeasurePointData::getValue)
                .sum();
        return photovoltaicAlgorithmService.calculateEquivalentOutputHour(sum, capacity);
    }

    private List<EnergyConsumption> queryEnergy(List<Long> authProjectIds, List<Long> logTimes, Integer aggregationCycle) {
        return new LambdaQueryChainWrapper<>(energyConsumptionMapper)
                .in(EnergyConsumption::getObjectId, authProjectIds)
                .eq(EnergyConsumption::getObjectLabel, TableNameConstant.PROJECT)
                .eq(EnergyConsumption::getAggregationCycle, aggregationCycle)
                .in(EnergyConsumption::getLogTime, logTimes)
                .eq(EnergyConsumption::getEnergyType, EnergyTypeEnum.ELECTRIC.getType())
                .eq(EnergyConsumption::getTimeSharePeriodIdentification, CharSequenceUtil.EMPTY)
                .list();
    }

    private void queryDeviceStatus(OverviewParam query, List<MeasuredBy> measuredByList, PlatformOverviewResponse platform,
                                   Map<String, List<Long>> labelToIdsMap) {
        List<Integer> pecDeviceIds = measuredByList.stream()
                .map(MeasuredBy::getMeasuredBy)
                .distinct()
                .collect(Collectors.toList());
        //获取设备在线情况
        Map<Integer, Integer> onlineMap = deviceDataExtendService.queryDeviceStatus(pecDeviceIds);
        //获取设备报警情况
        List<DeviceAlarmGroupCount> deviceAlarmGroupCountList = calcDeviceAlarmGroupCount(labelToIdsMap, query);
        Map<ModelInfo, DeviceAlarmGroupCount> deviceAlarmGroupCountMap = deviceAlarmGroupCountList.stream()
                .filter(self -> Objects.nonNull(self.getCount()) && self.getCount() > 0)
                .collect(Collectors.toMap(item -> new ModelInfo(item.getDeviceId(), item.getDeviceLabel()), self -> self));
        // 查询出可关联采集设备的设备label
        List<DeviceType> deviceTypes = new LambdaQueryChainWrapper<>(deviceTypeMapper)
                .eq(DeviceType::getEnable, Boolean.TRUE)
                .eq(DeviceType::getAssociativePecDevice, Boolean.TRUE)
                .list();
        Set<String> deviceLabels = deviceTypes.stream()
                .map(DeviceType::getDeviceLabel)
                .collect(Collectors.toSet());
        int onlineCount = 0;
        int alarmDeviceCount = 0;
        Map<ModelInfo, Integer> deviceInfoToPecMap = measuredByList.stream()
                .collect(Collectors.toMap(item -> new ModelInfo(item.getMonitoredId(), item.getMonitoredLabel()),
                        MeasuredBy::getMeasuredBy, (v1, v2) -> v1));
        List<ModelInfo> modelInfos = new ArrayList<>();
        for (Map.Entry<String, List<Long>> entry : labelToIdsMap.entrySet()) {
            for (Long deviceId : entry.getValue()) {
                ModelInfo modelInfo = new ModelInfo(deviceId, entry.getKey());
                modelInfos.add(modelInfo);
                Integer status = onlineMap.get(deviceInfoToPecMap.get(modelInfo));
                // 此管网设备需要支持关联采集设备，才进行在线状态的统计
                if (deviceLabels.contains(modelInfo.getModelLabel()) && Objects.nonNull(status) && (Objects.equals(0, status) || Objects.equals(1, status))) {
                    onlineCount++;
                }
                DeviceAlarmGroupCount deviceAlarmGroupCount = deviceAlarmGroupCountMap.get(modelInfo);
                if (Objects.nonNull(deviceAlarmGroupCount)) {
                    alarmDeviceCount++;
                }
            }
        }
        platform.setAlarmDeviceCount(alarmDeviceCount);
        platform.setDeviceCount(onlineCount);
        // 分母是支持关联采集设备并且关联了采集设备的管网设备的数量
        Set<LabelAndId> deviceModels = measuredByList.stream()
                .filter(it -> deviceLabels.contains(it.getMonitoredLabel()))
                .map(it -> new LabelAndId(it.getMonitoredLabel(), it.getMonitoredId()))
                .collect(Collectors.toSet());
        platform.setDeviceOnlineRate(DoubleUtil.divide((double) onlineCount, (double) deviceModels.size()) * 100);
        platform.setAlarmDevicePercent(DoubleUtil.divide((double) alarmDeviceCount, (double) modelInfos.size()) * 100);
        // 监测终端数量
        platform.setMeterCount(deviceModels.size());
    }

    private List<DeviceAlarmGroupCount> calcDeviceAlarmGroupCount(Map<String, List<Long>> labelToIdsMap, OverviewParam query) {
        LambdaQueryWrapper<SystemEvent> wrapper = new LambdaQueryWrapper<>(SystemEvent.class)
                .groupBy(SystemEvent::getObjectId, SystemEvent::getObjectLabel);
        for (Map.Entry<String, List<Long>> entry : labelToIdsMap.entrySet()) {
            wrapper.nested(n -> n.between(SystemEvent::getEventTime, query.getStartTime(), query.getEndTime())
                    .eq(SystemEvent::getConfirmEventStatus, ConfirmEventStatusEnum.UNCONFIRMED.getCode())
                    .in(SystemEvent::getObjectId, entry.getValue())
                    .eq(SystemEvent::getObjectLabel, entry.getKey())).or();
        }
        ModelQuery modelQuery = wrapper.build();
        ApiResult<List<DeviceAlarmGroupCount>> eventCountResult = modelExtendService.queryModelData(modelQuery, DeviceAlarmGroupCount.class);
        if (!eventCountResult.isSuccess()) {
            log.error("分组查询设备报警信息异常,{}", eventCountResult.getMsg());
            return Collections.emptyList();
        }
        return eventCountResult.getData();
    }

    private void setProcessingState(OverviewParam query,
                                    PlatformOverviewResponse platformResponse,
                                    List<EquipmentAccount> equipmentAccountList) {

        Map<String, List<Long>> labelToIdsMap = equipmentAccountList.stream()
                .collect(Collectors.groupingBy(EquipmentAccount::getModelLabel,
                        Collectors.mapping(EquipmentAccount::getId, Collectors.toList())));

        // 查询本月 未处理事件 最新的50条
        ListWithTotal<SystemEvent> unhandledAlarmList = getSystemEventList(query, labelToIdsMap, ConfirmEventStatusEnum.UNCONFIRMED.getCode());
        // 查询本月 处理中事件 最新的50条
        ListWithTotal<SystemEvent> confirmingList = getSystemEventList(query, labelToIdsMap, ConfirmEventStatusEnum.CONFIRMING.getCode());
        // 查询本月 已处理事件 最新的50条
        ListWithTotal<SystemEvent> confirmedList = getSystemEventList(query, labelToIdsMap, ConfirmEventStatusEnum.CONFIRMED.getCode());

        // 本月告警总数
        Integer thisTotal = NumberUtil.add(unhandledAlarmList.getTotal(), confirmingList.getTotal(), confirmedList.getTotal()).intValue();
        platformResponse.setAlarmCount(thisTotal);
        // 处理中
        platformResponse.setConfirmingNum(confirmingList.getTotal());
        // 已处理
        platformResponse.setHandledAlarmCount(confirmedList.getTotal());
        // 未处理
        platformResponse.setUnhandledAlarmCount(unhandledAlarmList.getTotal());

        List<SystemEvent> totalList = Stream.of(unhandledAlarmList.getList(), confirmingList.getList(), confirmedList.getList())
                .flatMap(List::stream)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(totalList)) {
            Map<String, EquipmentAccount> equipmentAccountMap = equipmentAccountList.stream()
                    .collect(Collectors.toMap(self -> (self.getModelLabel() + self.getId()), self -> self));
            platformResponse.setMatchDetailVos(convertToEventDetail(totalList, equipmentAccountMap));
        }
    }


    /**
     * 查询事件 最新的50条
     *
     * @return ListWithTotal
     */
    private ListWithTotal<SystemEvent> getSystemEventList(OverviewParam query,
                                                         Map<String, List<Long>> labelToIdsMap,
                                                         Integer confirmEventStatus) {
        LambdaQueryChainWrapper<SystemEvent> thisWrapper = new LambdaQueryChainWrapper<>(systemEventMapper)
                .orderByDesc(SystemEvent::getEventTime)
                .page(0, 50);
        for (Map.Entry<String, List<Long>> entry : labelToIdsMap.entrySet()) {
            thisWrapper.nested(n -> n.eq(SystemEvent::getObjectLabel, entry.getKey())
                            .between(SystemEvent::getEventTime, query.getStartTime(), query.getEndTime())
                            .in(SystemEvent::getObjectId, entry.getValue())
                            .eq(SystemEvent::getConfirmEventStatus, confirmEventStatus))
                    .or();
        }
        return thisWrapper.listWithTotal();
    }

    private List<EventMatchDetailVO> convertToEventDetail(List<SystemEvent> events,
                                                          Map<String, EquipmentAccount> equipmentAccountMap) {
        return events.stream().map(item -> {
            EventMatchDetailVO detailVO = new EventMatchDetailVO();
            detailVO.setStartEventLevel(item.getLevel());
            detailVO.setStartEventLevelText(item.getName());
            detailVO.setEventType(item.getEventType());
            detailVO.setEventTypeText(SystemEventTypeEnum.of(item.getEventType()));
            detailVO.setConfirmEventStatus(item.getConfirmEventStatus());
            detailVO.setConfirmEventStatusText(item.getConfirmEventStatusText());
            detailVO.setStartEventTime(item.getEventTime());
            detailVO.setStartEventDescription(item.getDescription());
            String key = item.getObjectLabel() + item.getObjectId();
            EquipmentAccount equipmentAccount = equipmentAccountMap.get(key);
            detailVO.setDeviceId(item.getObjectId());
            detailVO.setDeviceLabel(item.getObjectLabel());
            if (Objects.isNull(equipmentAccount)) {
                return detailVO;
            }
            detailVO.setBuildingId(equipmentAccount.getBuildingId());
            detailVO.setProjectId(equipmentAccount.getProjectId());
            detailVO.setRoomId(equipmentAccount.getRoomId());
            return detailVO;
        }).collect(Collectors.toList());
    }

    private List<Long> getAuthProjectIds() {
        return businessAuthService.getAuthNodeIds(AuthModelNodesEnum.PROJECT, AuthUtil.getUserId(), AuthUtil.getTenantId());
    }


    private void setPropertyValue(PlatformOverviewResponse platformOverviewResponse, List<EquipmentAccount> equipmentAccountList) {
        List<Long> deviceIds = equipmentAccountList.stream()
                .filter(it -> Objects.equals(it.getModelLabel(), TableNameConstant.POWER_TRANSFORMER))
                .map(EquipmentAccount::getId)
                .collect(Collectors.toList());
        // 额定总容量
        if (CollUtil.isNotEmpty(deviceIds)) {
            platformOverviewResponse.setTransformerCount(deviceIds.size());
            List<PowerTransformer> powerTransformers = powerTransformerMapper.selectBatchIds(deviceIds);
            platformOverviewResponse.setRatedTotalCapacity(
                    powerTransformers.stream()
                            .filter(it -> Objects.nonNull(it.getRatedCapacity()))
                            .mapToDouble(PowerTransformer::getRatedCapacity).sum());
        }
    }

    private void setSafeRunDays(List<Project> authProjects, PlatformOverviewResponse platformResponse) {
        authProjects.stream()
                .filter(project -> Objects.nonNull(project.getCommissionDate()))
                .map(project -> System.currentTimeMillis() - project.getCommissionDate())
                .max(Comparator.comparing(Long::valueOf))
                .ifPresent(maxTimeDelta -> platformResponse.setSafeRunDays(maxTimeDelta / OverviewConstant.DAY_INTERVAL));
    }

    private List<PeakVallyParams> supplyMonthPeakVallyDistributeData(Long lastMonth,
                                                                     Long thisMoth,
                                                                     List<EnergyConsumption> energyConsumptionList) {
        List<PeakVallyParams> nodeList = new ArrayList<>();
        List<String> types = Arrays.stream(EnergyUsageCurveTypeEnum.values())
                .map(EnergyUsageCurveTypeEnum::getName)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(energyConsumptionList)) {
            Map<String, Map<Long, List<EnergyConsumption>>> periodAndTimeGroup = energyConsumptionList
                    .stream()
                    .collect(Collectors.groupingBy(EnergyConsumption::getTimeSharePeriodIdentification,
                            Collectors.groupingBy(EnergyConsumption::getLogTime)));
            for (Map.Entry<String, Map<Long, List<EnergyConsumption>>> entry : periodAndTimeGroup.entrySet()) {
                String key = entry.getKey();
                List<EnergyConsumption> lastConsumptions = entry.getValue().get(lastMonth);
                List<EnergyConsumption> thisConsumptions = entry.getValue().get(thisMoth);
                if (key.equals(EnergyUsageCurveTypeEnum.TOP.getName() + EnergyUsageCurveTypeEnum.PEAK.getName())) {
                    key = EnergyUsageCurveTypeEnum.TOP.getName();
                }
                PeakVallyParams peakVallyParams = new PeakVallyParams();
                peakVallyParams.setName(key);
                if (CollUtil.isNotEmpty(lastConsumptions)) {
                    peakVallyParams.setLastValue(lastConsumptions.stream().mapToDouble(EnergyConsumption::getUsage).sum());
                }
                if (CollUtil.isNotEmpty(thisConsumptions)) {
                    peakVallyParams.setValue(thisConsumptions.stream().mapToDouble(EnergyConsumption::getUsage).sum());
                }
                peakVallyParams.setPeriodOverPeriodChange(calculatePeriodOverPeriodChange(peakVallyParams.getLastValue(), peakVallyParams.getValue()));
                nodeList.add(peakVallyParams);
            }
        }
        List<String> noneTypes = types.stream()
                .filter(item -> nodeList.stream().noneMatch(item2 -> item2.getName().equals(item)))
                .collect(Collectors.toList());
        for (String noneType : noneTypes) {
            nodeList.add(new PeakVallyParams(noneType, null, null, null));
        }
        return nodeList;
    }

    /**
     * 计算环比
     *
     * @param lastValue 上一周期的值
     * @param thisValue 下一周期的值
     * @return 环比
     */
    public Double calculatePeriodOverPeriodChange(Double lastValue, Double thisValue) {
        if (Objects.isNull(lastValue) || Objects.isNull(thisValue) || lastValue == 0) {
            return null;
        }

        return ((thisValue - lastValue) / lastValue);
    }

    public List<Dim2Energy> queryDim2EnergyByNodes(List<LabelAndId> nodeList, Integer energyType) {
        Long now = new Date().getTime();
        LambdaQueryChainWrapper<Dim2Energy> lambdaQueryChainWrapper = new LambdaQueryChainWrapper<>(dim2EnergyMapper);
        nodeList.forEach(node -> lambdaQueryChainWrapper.or(a ->
                a.le(Dim2Energy::getEnableTime, now)
                        .eq(Dim2Energy::getEnergytype, energyType, Objects.nonNull(energyType))
                        .eq(Dim2Energy::getObjectLabel, node.getModelLabel(), StringUtils.isNotEmpty(node.getModelLabel()))
                        .eq(Dim2Energy::getObjectId, node.getId(), Objects.nonNull(node.getId()))
                        .eq(Dim2Energy::getDisabletime, null)));
        return lambdaQueryChainWrapper.list();
    }

    private List<MultiDimEnergyConsumption> getQuantityAggregationDataList(Long lastMonth,
                                                                           Long thisMonth,
                                                                           List<Dim2Energy> dim2Energies) {
        LambdaQueryChainWrapper<MultiDimEnergyConsumption> queryChainWrapper
                = new LambdaQueryChainWrapper<>(multiDimEnergyConsumptionMapper);
        dim2Energies.forEach(dim2Energy -> {
            //组内and , 组外or
            queryChainWrapper.nested(n -> n
                            .eq(MultiDimEnergyConsumption::getObjectlabel, dim2Energy.getObjectLabel())
                            .eq(MultiDimEnergyConsumption::getObjectId, dim2Energy.getObjectId())
                            .eq(MultiDimEnergyConsumption::getAggregationcycle, AggregationCycleEnum.MONTH.getId())
                            .in(MultiDimEnergyConsumption::getLogtime, Arrays.asList(lastMonth, thisMonth)))
                    .or();
        });


        return queryChainWrapper.list();
    }


    public List<MoEventCount> queryEventCounts(EventAnalysisParams query) {
        ParamsAssert.notNull(query.getAggregationCycle());
        ParamsAssert.notNull(query.getStartTime());
        ParamsAssert.notNull(query.getEndTime());
        ParamsAssert.notEmpty(query.getModelIds());
        ParamsAssert.notNull(query.getModelLabel());
        LambdaQueryChainWrapper<MoEventCount> wrapper = new LambdaQueryChainWrapper<>(moEventCountMapper);
        wrapper.eq(MoEventCount::getAggregationCycle, query.getAggregationCycle())
                .eq(MoEventCount::getEventLabel, TableNameConstant.SYSTEM_EVENT)
                .ge(MoEventCount::getLogTime, query.getStartTime())
                .lt(MoEventCount::getLogTime, query.getEndTime());

        wrapper.eq(MoEventCount::getMonitoredLabel, query.getModelLabel())
                .in(MoEventCount::getMonitoredId, query.getModelIds());

        if (CollUtil.isNotEmpty(query.getEventLevelList())) {
            wrapper.in(MoEventCount::getEventLevel, query.getEventLevelList());
        }
        if (CollUtil.isNotEmpty(query.getEventTypeList())) {
            wrapper.in(MoEventCount::getEventType, query.getEventTypeList());
        }
        return wrapper.list();
    }

    private List<Room> getRoomList(String nodeLabel, Long nodeId, List<Integer> roomTypes) {
        LambdaQueryChainWrapper<Room> wrapper = new LambdaQueryChainWrapper<>(roomMapper);
        if (Objects.nonNull(nodeId)) {
            AuthModelNodesEnum authModelNodeEnum = AuthModelNodesEnum.getEnum(nodeLabel);
            businessAuthService.checkNodeAuth(authModelNodeEnum, nodeId);
            switch (authModelNodeEnum) {
                case PROJECT:
                    wrapper.treeNode(Project.class, nodeId);
                    break;
                case BUILDING:
                    wrapper.treeNode(Building.class, nodeId);
                    break;
                case FLOOR:
                    wrapper.treeNode(Floor.class, nodeId);
                    break;
                case ROOM:
                    wrapper.eq(Room::getId, nodeId);
                    break;
                default:
                    break;
            }
        }
        wrapper.in(Room::getRoomType, roomTypes);
        List<Room> roomList = wrapper.list();
        // 非系统管理员 鉴权
        if (!businessAuthService.isSystemManager()) {
            List<Long> authRoomIds = businessAuthService.getAuthNodeIds(AuthModelNodesEnum.ROOM);
            roomList = roomList.stream()
                    .filter(item -> CollUtil.contains(authRoomIds, item.getId()))
                    .collect(Collectors.toList());
        }
        return roomList;
    }

    private EventTimelinessDTO getTimeliness(Long startTime,
                                             Long endTime,
                                             Map<String, List<Long>> labelToIdsMap) {
        LambdaQueryChainWrapper<SystemEvent> wrapper = new LambdaQueryChainWrapper<>(systemEventMapper);
        for (Map.Entry<String, List<Long>> entry : labelToIdsMap.entrySet()) {
            wrapper.nested(n -> n
                    .eq(SystemEvent::getObjectLabel, entry.getKey())
                    .between(SystemEvent::getEventTime, startTime, endTime)
                    .in(SystemEvent::getObjectId, entry.getValue())).or();
        }
        List<SystemEvent> eventList = wrapper
                .props(SystemEvent::getConfirmEventStatus, SystemEvent::getUpdateTime, SystemEvent::getEventTime)
                .list();
        if (CollUtil.isEmpty(eventList)) {
            return new EventTimelinessDTO();
        }
        //事件总数
        long eventTotal = eventList.size();
        //未处理
        long unprocessedEventCount = 0;
        //一小时内处理
        long oneHourProcess = 0;
        //一天内处理
        long oneDayProcess = 0;
        //两天内处理
        long towDayProcess = 0;
        //一周内处理
        long oneWeekProcess = 0;
        for (SystemEvent event : eventList) {
            Integer confirmEventStatus = event.getConfirmEventStatus();
            if (Objects.equals(confirmEventStatus, ConfirmEventStatusEnum.UNCONFIRMED.getCode())) {
                unprocessedEventCount++;
            }
            if (!Objects.equals(confirmEventStatus, ConfirmEventStatusEnum.CONFIRMED.getCode())) {
                continue;
            }
            long differTime = event.getUpdateTime() - event.getEventTime();
            if (differTime <= OverviewConstant.HOUR_INTERVAL) {
                oneHourProcess++;
            } else if (differTime <= OverviewConstant.DAY_INTERVAL) {
                oneDayProcess++;
            } else if (differTime <= OverviewConstant.TWO_DAY_INTERVAL) {
                towDayProcess++;
            } else if (differTime <= OverviewConstant.WEEK_INTERVAL) {
                oneWeekProcess++;
            }
        }
        oneDayProcess = oneDayProcess + oneHourProcess;
        towDayProcess = towDayProcess + oneDayProcess;
        oneWeekProcess = oneWeekProcess + towDayProcess;
        return new EventTimelinessDTO(eventTotal, unprocessedEventCount, oneHourProcess, oneDayProcess, towDayProcess, oneWeekProcess);
    }
}
